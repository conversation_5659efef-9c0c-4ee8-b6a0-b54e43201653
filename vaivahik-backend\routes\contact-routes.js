/**
 * Contact Reveal Routes
 * API endpoints for smart contact reveal system
 * Cross-platform support: Web, Android, iOS
 */

const express = require('express');
const router = express.Router();
const { authenticateUser } = require('../src/middleware/auth.middleware');
const contactRevealService = require('../services/contact/contact-reveal-service');

/**
 * @route POST /api/contact/reveal/:userId
 * @desc Request contact details of another user
 * @access Private (Premium feature)
 */
router.post('/reveal/:userId', authenticateUser, async (req, res) => {
  try {
    const accessorId = req.user.id;
    const contactOwnerId = req.params.userId;
    const platform = req.headers['x-platform'] || req.body.platform || 'WEB';

    if (!contactOwnerId) {
      return res.status(400).json({
        success: false,
        error: 'MISSING_USER_ID',
        message: 'User ID is required'
      });
    }

    // Get security information
    const ipAddress = req.ip || req.connection.remoteAddress || req.headers['x-forwarded-for'];
    const userAgent = req.headers['user-agent'];

    const result = await contactRevealService.checkContactAccess(
      accessorId,
      contactOwnerId,
      platform,
      ipAddress,
      userAgent
    );

    if (!result.success) {
      const statusCode = result.error === 'PREMIUM_REQUIRED' ? 402 : 403;
      return res.status(statusCode).json(result);
    }

    // Generate platform-specific dialer URL
    const dialerUrl = contactRevealService.generateDialerUrl(result.contactNumber, platform);

    res.json({
      success: true,
      contactNumber: result.contactNumber,
      contactOwnerName: result.contactOwnerName,
      dialerUrl,
      callAvailability: result.callAvailability,
      accessReason: result.accessReason,
      platform
    });

  } catch (error) {
    console.error('Error in contact reveal:', error);
    res.status(500).json({
      success: false,
      error: 'SERVER_ERROR',
      message: 'Unable to process contact reveal request'
    });
  }
});

/**
 * @route GET /api/contact/can-access/:userId
 * @desc Check if user can access contact without revealing it
 * @access Private
 */
router.get('/can-access/:userId', authenticateUser, async (req, res) => {
  try {
    const accessorId = req.user.id;
    const contactOwnerId = req.params.userId;
    const platform = req.headers['x-platform'] || 'WEB';

    // Get security information
    const ipAddress = req.ip || req.connection.remoteAddress || req.headers['x-forwarded-for'];
    const userAgent = req.headers['user-agent'];

    const result = await contactRevealService.checkContactAccess(
      accessorId,
      contactOwnerId,
      platform,
      ipAddress,
      userAgent
    );

    // Return access status without revealing contact
    res.json({
      success: true,
      canAccess: result.success,
      reason: result.error || 'ACCESS_GRANTED',
      message: result.message,
      upgradeRequired: result.upgradeRequired || false
    });

  } catch (error) {
    console.error('Error checking contact access:', error);
    res.status(500).json({
      success: false,
      error: 'SERVER_ERROR',
      message: 'Unable to check contact access'
    });
  }
});

/**
 * @route GET /api/contact/access-history
 * @desc Get contact access history for current user
 * @access Private
 */
router.get('/access-history', authenticateUser, async (req, res) => {
  try {
    const userId = req.user.id;
    const limit = parseInt(req.query.limit) || 50;

    const result = await contactRevealService.getContactAccessHistory(userId, limit);

    if (!result.success) {
      return res.status(500).json(result);
    }

    res.json({
      success: true,
      history: result.history.map(log => ({
        id: log.id,
        accessType: log.accessType,
        accessReason: log.accessReason,
        platform: log.platform,
        accessedAt: log.accessedAt,
        isAccessor: log.accessorId === userId,
        otherUser: log.accessorId === userId ? {
          id: log.contactOwner.id,
          name: log.contactOwner.profile?.fullName || 'User',
          profilePic: log.contactOwner.profile?.profilePic
        } : {
          id: log.accessor.id,
          name: log.accessor.profile?.fullName || 'User',
          profilePic: log.accessor.profile?.profilePic
        }
      }))
    });

  } catch (error) {
    console.error('Error fetching contact access history:', error);
    res.status(500).json({
      success: false,
      error: 'SERVER_ERROR',
      message: 'Unable to fetch contact access history'
    });
  }
});

/**
 * @route PUT /api/contact/privacy-settings
 * @desc Update contact privacy settings
 * @access Private
 */
router.put('/privacy-settings', authenticateUser, async (req, res) => {
  try {
    const userId = req.user.id;
    const {
      allowDirectCalls,
      contactRevealPreference,
      requireMutualInterest,
      callAvailability
    } = req.body;

    // Validate contact reveal preference
    const validPreferences = ['PREMIUM_ONLY', 'MUTUAL_INTEREST', 'ACCEPTED_INTEREST', 'NEVER'];
    if (contactRevealPreference && !validPreferences.includes(contactRevealPreference)) {
      return res.status(400).json({
        success: false,
        error: 'INVALID_PREFERENCE',
        message: 'Invalid contact reveal preference'
      });
    }

    // Validate call availability
    const validAvailability = ['ANYTIME', 'BUSINESS_HOURS', 'EVENING_ONLY', 'WEEKEND_ONLY'];
    if (callAvailability && !validAvailability.includes(callAvailability)) {
      return res.status(400).json({
        success: false,
        error: 'INVALID_AVAILABILITY',
        message: 'Invalid call availability setting'
      });
    }

    const result = await contactRevealService.updateContactPrivacySettings(userId, {
      allowDirectCalls,
      contactRevealPreference,
      requireMutualInterest,
      callAvailability
    });

    if (!result.success) {
      return res.status(500).json(result);
    }

    res.json({
      success: true,
      message: 'Contact privacy settings updated successfully',
      settings: result.settings
    });

  } catch (error) {
    console.error('Error updating contact privacy settings:', error);
    res.status(500).json({
      success: false,
      error: 'SERVER_ERROR',
      message: 'Unable to update contact privacy settings'
    });
  }
});

/**
 * @route GET /api/contact/privacy-settings
 * @desc Get current contact privacy settings
 * @access Private
 */
router.get('/privacy-settings', authenticateUser, async (req, res) => {
  try {
    const userId = req.user.id;

    const { PrismaClient } = require('@prisma/client');
    const prisma = new PrismaClient();

    const profile = await prisma.profile.findUnique({
      where: { userId },
      select: {
        allowDirectCalls: true,
        contactRevealPreference: true,
        requireMutualInterest: true,
        callAvailability: true
      }
    });

    if (!profile) {
      return res.status(404).json({
        success: false,
        error: 'PROFILE_NOT_FOUND',
        message: 'Profile not found'
      });
    }

    res.json({
      success: true,
      settings: profile
    });

  } catch (error) {
    console.error('Error fetching contact privacy settings:', error);
    res.status(500).json({
      success: false,
      error: 'SERVER_ERROR',
      message: 'Unable to fetch contact privacy settings'
    });
  } finally {
    await prisma.$disconnect();
  }
});

/**
 * @route GET /api/contact/options
 * @desc Get available contact reveal options and preferences
 * @access Private
 */
router.get('/options', authenticateUser, async (req, res) => {
  try {
    const options = {
      contactRevealPreferences: [
        {
          value: 'PREMIUM_ONLY',
          label: 'Premium Users Only',
          description: 'Only premium subscribers can access your contact',
          icon: '💎'
        },
        {
          value: 'MUTUAL_INTEREST',
          label: 'Mutual Interest',
          description: 'Both users must show interest',
          icon: '💕'
        },
        {
          value: 'ACCEPTED_INTEREST',
          label: 'Accepted Interest',
          description: 'Only after interest is accepted',
          icon: '✅'
        },
        {
          value: 'NEVER',
          label: 'Never Share',
          description: 'Keep contact information private',
          icon: '🔒'
        }
      ],
      callAvailability: [
        {
          value: 'ANYTIME',
          label: 'Anytime',
          description: 'Available for calls anytime',
          icon: '📞'
        },
        {
          value: 'BUSINESS_HOURS',
          label: 'Business Hours',
          description: '9 AM to 6 PM only',
          icon: '🕘'
        },
        {
          value: 'EVENING_ONLY',
          label: 'Evening Only',
          description: '6 PM to 10 PM only',
          icon: '🌆'
        },
        {
          value: 'WEEKEND_ONLY',
          label: 'Weekends Only',
          description: 'Saturday and Sunday only',
          icon: '📅'
        }
      ]
    };

    res.json({
      success: true,
      options
    });

  } catch (error) {
    console.error('Error fetching contact options:', error);
    res.status(500).json({
      success: false,
      error: 'SERVER_ERROR',
      message: 'Unable to fetch contact options'
    });
  }
});

module.exports = router;
