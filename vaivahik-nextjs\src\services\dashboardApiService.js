/**
 * Dashboard API Service
 * Handles all dashboard-related API calls
 */

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api';

class DashboardApiService {
  constructor() {
    this.baseURL = API_BASE_URL;
  }

  // Helper method to get auth headers
  getAuthHeaders() {
    const token = localStorage.getItem('token');
    return {
      'Content-Type': 'application/json',
      'Authorization': token ? `Bearer ${token}` : ''
    };
  }

  // Helper method to handle API responses
  async handleResponse(response) {
    if (!response.ok) {
      const error = await response.json().catch(() => ({ message: 'Network error' }));
      throw new Error(error.message || 'API request failed');
    }
    return response.json();
  }

  // Dashboard Stats
  async getDashboardStats() {
    try {
      const response = await fetch(`${this.baseURL}/dashboard/stats`, {
        method: 'GET',
        headers: this.getAuthHeaders()
      });
      return this.handleResponse(response);
    } catch (error) {
      console.error('Error fetching dashboard stats:', error);
      // Return mock data as fallback
      return {
        success: true,
        stats: {
          profileViews: 127,
          interests: 23,
          messages: 8,
          matches: 45
        }
      };
    }
  }

  // Recent Matches
  async getRecentMatches(limit = 6) {
    try {
      const response = await fetch(`${this.baseURL}/dashboard/recent-matches?limit=${limit}`, {
        method: 'GET',
        headers: this.getAuthHeaders()
      });
      return this.handleResponse(response);
    } catch (error) {
      console.error('Error fetching recent matches:', error);
      // Return mock data as fallback
      return {
        success: true,
        matches: [
          {
            id: 1,
            name: 'Priya Sharma',
            age: 26,
            location: 'Mumbai',
            photo: '/api/placeholder/150/150',
            compatibility: 94,
            isOnline: true
          },
          {
            id: 2,
            name: 'Anita Patil',
            age: 24,
            location: 'Pune',
            photo: '/api/placeholder/150/150',
            compatibility: 89,
            isOnline: false
          },
          {
            id: 3,
            name: 'Kavya Desai',
            age: 27,
            location: 'Nashik',
            photo: '/api/placeholder/150/150',
            compatibility: 87,
            isOnline: true
          }
        ]
      };
    }
  }

  // Verification Status
  async getVerificationStatus() {
    try {
      const response = await fetch(`${this.baseURL}/dashboard/verification-status`, {
        method: 'GET',
        headers: this.getAuthHeaders()
      });
      return this.handleResponse(response);
    } catch (error) {
      console.error('Error fetching verification status:', error);
      // Return mock data as fallback
      return {
        success: true,
        verification: {
          overall: 'PENDING',
          photo: 'VERIFIED',
          document: 'PENDING',
          phone: 'VERIFIED',
          email: 'VERIFIED'
        }
      };
    }
  }

  // Recent Chats
  async getRecentChats(limit = 5) {
    try {
      const response = await fetch(`${this.baseURL}/dashboard/recent-chats?limit=${limit}`, {
        method: 'GET',
        headers: this.getAuthHeaders()
      });
      return this.handleResponse(response);
    } catch (error) {
      console.error('Error fetching recent chats:', error);
      // Return mock data as fallback
      return {
        success: true,
        chats: [
          {
            id: 1,
            name: 'Priya Sharma',
            avatar: '/api/placeholder/50/50',
            lastMessage: 'Thank you for your interest! I would love to know more about you.',
            timestamp: '2 min ago',
            unreadCount: 2,
            isOnline: true,
            isPremiumUser: true
          },
          {
            id: 2,
            name: 'Anita Patil',
            avatar: '/api/placeholder/50/50',
            lastMessage: 'Hi! I saw your profile and found it interesting.',
            timestamp: '1 hour ago',
            unreadCount: 0,
            isOnline: false,
            isPremiumUser: false
          }
        ]
      };
    }
  }

  // Biodata Templates
  async getBiodataTemplates(gender = 'male') {
    try {
      const response = await fetch(`${this.baseURL}/dashboard/biodata-templates?gender=${gender}`, {
        method: 'GET',
        headers: this.getAuthHeaders()
      });
      return this.handleResponse(response);
    } catch (error) {
      console.error('Error fetching biodata templates:', error);
      // Return mock data as fallback
      return {
        success: true,
        templates: [
          {
            id: 1,
            name: 'Classic Maratha',
            description: 'Traditional Maratha biodata with elegant design',
            thumbnail: '/api/placeholder/200/280',
            genderOrientation: gender,
            category: 'free',
            price: 0,
            isPremium: false,
            features: ['Traditional Design', 'Maratha Symbols', 'Family Tree Section']
          },
          {
            id: 2,
            name: 'Royal Heritage',
            description: 'Premium design with royal Maratha heritage elements',
            thumbnail: '/api/placeholder/200/280',
            genderOrientation: gender,
            category: 'premium',
            price: 299,
            isPremium: true,
            features: ['Royal Design', 'Gold Accents', 'Premium Layout', 'Horoscope Section']
          }
        ]
      };
    }
  }

  // Spotlight Options
  async getSpotlightOptions() {
    try {
      const response = await fetch(`${this.baseURL}/dashboard/spotlight-options`, {
        method: 'GET',
        headers: this.getAuthHeaders()
      });
      return this.handleResponse(response);
    } catch (error) {
      console.error('Error fetching spotlight options:', error);
      // Return mock data as fallback
      return {
        success: true,
        options: [
          {
            id: 1,
            name: 'Basic Spotlight',
            duration: '24 hours',
            price: 199,
            description: 'Boost your profile visibility for 24 hours',
            features: [
              '3x more profile views',
              'Priority in search results',
              'Featured in spotlight section'
            ]
          },
          {
            id: 2,
            name: 'Premium Spotlight',
            duration: '7 days',
            price: 999,
            description: 'Premium visibility boost for a full week',
            features: [
              '5x more profile views',
              'Top priority in search',
              'Featured in spotlight section',
              'Special badge on profile',
              'Push notifications to matches'
            ],
            popular: true
          }
        ]
      };
    }
  }

  // Subscription Plans
  async getSubscriptionPlans() {
    try {
      const response = await fetch(`${this.baseURL}/dashboard/subscription-plans`, {
        method: 'GET',
        headers: this.getAuthHeaders()
      });
      return this.handleResponse(response);
    } catch (error) {
      console.error('Error fetching subscription plans:', error);
      // Return mock data as fallback
      return {
        success: true,
        plans: [
          {
            id: 'monthly',
            name: 'Monthly Premium',
            price: 999,
            originalPrice: 1299,
            duration: '1 Month',
            savings: null,
            popular: false,
            features: [
              'Unlimited profile views',
              'Direct contact details access',
              'Advanced search filters',
              'Priority in search results',
              'Basic chat features',
              'Email support'
            ]
          },
          {
            id: 'quarterly',
            name: 'Quarterly Premium',
            price: 2499,
            originalPrice: 3897,
            duration: '3 Months',
            savings: '36% OFF',
            popular: true,
            features: [
              'All Monthly features',
              'Unlimited messaging',
              'Voice calling feature',
              'Profile boost every month',
              'Priority customer support',
              'Advanced matching algorithm'
            ]
          }
        ]
      };
    }
  }

  // Profile Completion Data
  async getProfileCompletionData() {
    try {
      const response = await fetch(`${this.baseURL}/dashboard/profile-completion`, {
        method: 'GET',
        headers: this.getAuthHeaders()
      });
      return this.handleResponse(response);
    } catch (error) {
      console.error('Error fetching profile completion data:', error);
      // Return mock data as fallback
      return {
        success: true,
        completion: {
          overallPercentage: 65,
          sections: [
            {
              id: 'basic',
              name: 'Basic Information',
              completed: true,
              priority: 'high'
            },
            {
              id: 'photos',
              name: 'Profile Photos',
              completed: true,
              priority: 'high'
            },
            {
              id: 'education',
              name: 'Education & Career',
              completed: false,
              priority: 'medium'
            },
            {
              id: 'family',
              name: 'Family Details',
              completed: false,
              priority: 'medium'
            }
          ]
        }
      };
    }
  }

  // Upload verification document
  async uploadVerificationDocument(type, file) {
    try {
      const formData = new FormData();
      formData.append('type', type);
      formData.append('file', file);

      const response = await fetch(`${this.baseURL}/dashboard/verification/upload`, {
        method: 'POST',
        headers: {
          'Authorization': this.getAuthHeaders().Authorization
        },
        body: formData
      });
      return this.handleResponse(response);
    } catch (error) {
      console.error('Error uploading verification document:', error);
      throw error;
    }
  }

  // Purchase biodata template
  async purchaseBiodataTemplate(templateId) {
    try {
      const response = await fetch(`${this.baseURL}/dashboard/biodata/purchase`, {
        method: 'POST',
        headers: this.getAuthHeaders(),
        body: JSON.stringify({ templateId })
      });
      return this.handleResponse(response);
    } catch (error) {
      console.error('Error purchasing biodata template:', error);
      throw error;
    }
  }

  // Purchase spotlight
  async purchaseSpotlight(spotlightId) {
    try {
      const response = await fetch(`${this.baseURL}/dashboard/spotlight/purchase`, {
        method: 'POST',
        headers: this.getAuthHeaders(),
        body: JSON.stringify({ spotlightId })
      });
      return this.handleResponse(response);
    } catch (error) {
      console.error('Error purchasing spotlight:', error);
      throw error;
    }
  }
}

// Create and export a singleton instance
const dashboardApiService = new DashboardApiService();
export default dashboardApiService;
