/**
 * Profile Completion Widget Component
 * Shows profile completion percentage and remaining fields
 */

import { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  LinearProgress,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Avatar,
  Chip,
  Alert,
  IconButton,
  Tooltip,
  Divider
} from '@mui/material';
import { styled } from '@mui/material/styles';
import {
  AccountCircle as ProfileIcon,
  CheckCircle as CheckIcon,
  RadioButtonUnchecked as UncheckedIcon,
  Edit as EditIcon,
  PhotoCamera as PhotoIcon,
  Work as WorkIcon,
  Home as HomeIcon,
  Favorite as InterestIcon,
  Description as AboutIcon,
  School as EducationIcon,
  TrendingUp as ProgressIcon,
  Star as StarIcon
} from '@mui/icons-material';

const GlassCard = styled(Card)(({ theme }) => ({
  background: 'rgba(255, 255, 255, 0.95)',
  backdropFilter: 'blur(20px)',
  border: '1px solid rgba(255, 255, 255, 0.2)',
  borderRadius: 24,
  boxShadow: `
    0 20px 40px rgba(0, 0, 0, 0.1),
    0 8px 32px rgba(255, 182, 193, 0.2)
  `,
  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
  position: 'relative',
  overflow: 'hidden',
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: 4,
    background: 'linear-gradient(90deg, #4CAF50, #8BC34A, #CDDC39)',
    borderRadius: '24px 24px 0 0'
  }
}));

const ProfileCompletionWidget = ({ userId, userData = {} }) => {
  const [completionData, setCompletionData] = useState({
    overallPercentage: 0,
    sections: []
  });

  useEffect(() => {
    calculateCompletion();
  }, [userData]);

  const calculateCompletion = () => {
    const sections = [
      {
        id: 'basic',
        name: 'Basic Information',
        icon: <ProfileIcon />,
        fields: ['name', 'age', 'gender', 'maritalStatus'],
        completed: checkFieldsCompleted(['name', 'age', 'gender', 'maritalStatus']),
        route: '/profile/edit/basic',
        priority: 'high'
      },
      {
        id: 'photos',
        name: 'Profile Photos',
        icon: <PhotoIcon />,
        fields: ['profilePhoto', 'additionalPhotos'],
        completed: checkFieldsCompleted(['profilePhoto']),
        route: '/profile/edit/photos',
        priority: 'high'
      },
      {
        id: 'education',
        name: 'Education & Career',
        icon: <EducationIcon />,
        fields: ['education', 'occupation', 'income'],
        completed: checkFieldsCompleted(['education', 'occupation']),
        route: '/profile/edit/education',
        priority: 'medium'
      },
      {
        id: 'family',
        name: 'Family Details',
        icon: <HomeIcon />,
        fields: ['fatherOccupation', 'motherOccupation', 'siblings'],
        completed: checkFieldsCompleted(['fatherOccupation', 'motherOccupation']),
        route: '/profile/edit/family',
        priority: 'medium'
      },
      {
        id: 'lifestyle',
        name: 'Lifestyle & Habits',
        icon: <InterestIcon />,
        fields: ['diet', 'smoking', 'drinking', 'hobbies'],
        completed: checkFieldsCompleted(['diet', 'hobbies']),
        route: '/profile/edit/lifestyle',
        priority: 'low'
      },
      {
        id: 'about',
        name: 'About Me',
        icon: <AboutIcon />,
        fields: ['aboutMe', 'partnerExpectations'],
        completed: checkFieldsCompleted(['aboutMe']),
        route: '/profile/edit/about',
        priority: 'high'
      }
    ];

    const completedSections = sections.filter(section => section.completed).length;
    const overallPercentage = Math.round((completedSections / sections.length) * 100);

    setCompletionData({
      overallPercentage,
      sections
    });
  };

  const checkFieldsCompleted = (fields) => {
    return fields.some(field => userData[field] && userData[field] !== '');
  };

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'high': return '#F44336';
      case 'medium': return '#FF9800';
      case 'low': return '#4CAF50';
      default: return '#9E9E9E';
    }
  };

  const getPriorityLabel = (priority) => {
    switch (priority) {
      case 'high': return 'High Priority';
      case 'medium': return 'Medium Priority';
      case 'low': return 'Optional';
      default: return '';
    }
  };

  const getCompletionMessage = () => {
    if (completionData.overallPercentage >= 90) {
      return {
        message: "Excellent! Your profile is almost complete. You'll get maximum visibility!",
        severity: 'success'
      };
    } else if (completionData.overallPercentage >= 70) {
      return {
        message: "Good progress! Complete a few more sections to boost your profile visibility.",
        severity: 'info'
      };
    } else if (completionData.overallPercentage >= 50) {
      return {
        message: "You're halfway there! Complete more sections to attract better matches.",
        severity: 'warning'
      };
    } else {
      return {
        message: "Your profile needs attention. Complete more sections to improve match quality.",
        severity: 'error'
      };
    }
  };

  const handleEditSection = (route) => {
    window.location.href = route;
  };

  const completionMessage = getCompletionMessage();

  return (
    <GlassCard>
      <CardContent sx={{ p: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
          <ProgressIcon sx={{ color: '#4CAF50', mr: 2, fontSize: 28 }} />
          <Typography variant="h6" fontWeight="600">
            Profile Completion
          </Typography>
          <Chip
            label={`${completionData.overallPercentage}%`}
            size="small"
            sx={{
              ml: 'auto',
              background: `linear-gradient(135deg, ${
                completionData.overallPercentage >= 80 ? '#4CAF50' : 
                completionData.overallPercentage >= 60 ? '#FF9800' : '#F44336'
              }, ${
                completionData.overallPercentage >= 80 ? '#8BC34A' : 
                completionData.overallPercentage >= 60 ? '#FFB74D' : '#EF5350'
              })`,
              color: 'white',
              fontWeight: 600
            }}
          />
        </Box>

        {/* Overall Progress */}
        <Box sx={{ mb: 3 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
            <Typography variant="body2" fontWeight="600">
              Overall Progress
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {completionData.sections.filter(s => s.completed).length}/{completionData.sections.length} sections
            </Typography>
          </Box>
          <LinearProgress
            variant="determinate"
            value={completionData.overallPercentage}
            sx={{
              height: 12,
              borderRadius: 6,
              backgroundColor: 'rgba(76, 175, 80, 0.1)',
              '& .MuiLinearProgress-bar': {
                background: 'linear-gradient(90deg, #4CAF50, #8BC34A)',
                borderRadius: 6
              }
            }}
          />
        </Box>

        {/* Completion Message */}
        <Alert severity={completionMessage.severity} sx={{ mb: 3, borderRadius: 2 }}>
          <Typography variant="body2">
            {completionMessage.message}
          </Typography>
        </Alert>

        {/* Profile Sections */}
        <Typography variant="subtitle2" fontWeight="600" gutterBottom>
          Complete Your Profile Sections
        </Typography>
        
        <List sx={{ p: 0 }}>
          {completionData.sections.map((section, index) => (
            <Box key={section.id}>
              <ListItem
                sx={{
                  borderRadius: 2,
                  mb: 1,
                  backgroundColor: section.completed 
                    ? 'rgba(76, 175, 80, 0.05)' 
                    : 'rgba(255, 152, 0, 0.05)',
                  border: `1px solid ${
                    section.completed 
                      ? 'rgba(76, 175, 80, 0.2)' 
                      : 'rgba(255, 152, 0, 0.2)'
                  }`
                }}
              >
                <ListItemIcon>
                  <Avatar
                    sx={{
                      width: 40,
                      height: 40,
                      backgroundColor: section.completed ? '#4CAF50' : getPriorityColor(section.priority),
                      color: 'white'
                    }}
                  >
                    {section.icon}
                  </Avatar>
                </ListItemIcon>
                <ListItemText
                  primary={
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Typography variant="subtitle2" fontWeight="600">
                        {section.name}
                      </Typography>
                      {section.completed ? (
                        <CheckIcon sx={{ color: '#4CAF50', fontSize: 18 }} />
                      ) : (
                        <UncheckedIcon sx={{ color: 'text.secondary', fontSize: 18 }} />
                      )}
                    </Box>
                  }
                  secondary={
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mt: 0.5 }}>
                      <Chip
                        label={getPriorityLabel(section.priority)}
                        size="small"
                        sx={{
                          backgroundColor: `${getPriorityColor(section.priority)}20`,
                          color: getPriorityColor(section.priority),
                          fontSize: '0.7rem',
                          height: 20
                        }}
                      />
                      {section.completed && (
                        <Chip
                          label="Completed"
                          size="small"
                          sx={{
                            backgroundColor: 'rgba(76, 175, 80, 0.1)',
                            color: '#4CAF50',
                            fontSize: '0.7rem',
                            height: 20
                          }}
                        />
                      )}
                    </Box>
                  }
                />
                <Tooltip title={section.completed ? 'Edit Section' : 'Complete Section'}>
                  <IconButton
                    size="small"
                    onClick={() => handleEditSection(section.route)}
                    sx={{
                      color: section.completed ? '#4CAF50' : '#FF9800',
                      '&:hover': {
                        backgroundColor: section.completed 
                          ? 'rgba(76, 175, 80, 0.1)' 
                          : 'rgba(255, 152, 0, 0.1)'
                      }
                    }}
                  >
                    <EditIcon fontSize="small" />
                  </IconButton>
                </Tooltip>
              </ListItem>
              {index < completionData.sections.length - 1 && <Divider />}
            </Box>
          ))}
        </List>

        {/* Benefits of Complete Profile */}
        {completionData.overallPercentage < 100 && (
          <Box sx={{ mt: 3, p: 2, borderRadius: 2, backgroundColor: 'rgba(255, 215, 0, 0.1)', border: '1px solid rgba(255, 215, 0, 0.3)' }}>
            <Typography variant="subtitle2" fontWeight="600" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <StarIcon sx={{ color: '#FFD700', fontSize: 18 }} />
              Benefits of 100% Profile Completion
            </Typography>
            <List dense sx={{ p: 0 }}>
              {[
                '5x more profile views',
                'Higher match compatibility scores',
                'Priority in search results',
                'Access to premium matching algorithm'
              ].map((benefit, index) => (
                <ListItem key={index} sx={{ py: 0, px: 0 }}>
                  <ListItemIcon sx={{ minWidth: 20 }}>
                    <CheckIcon sx={{ fontSize: 14, color: '#FFD700' }} />
                  </ListItemIcon>
                  <ListItemText
                    primary={
                      <Typography variant="body2" sx={{ fontSize: '0.85rem' }}>
                        {benefit}
                      </Typography>
                    }
                  />
                </ListItem>
              ))}
            </List>
          </Box>
        )}

        {/* Action Button */}
        <Box sx={{ mt: 3, textAlign: 'center' }}>
          <Button
            variant="contained"
            fullWidth
            startIcon={<EditIcon />}
            onClick={() => window.location.href = '/profile/edit'}
            sx={{
              background: 'linear-gradient(135deg, #4CAF50, #8BC34A)',
              '&:hover': {
                background: 'linear-gradient(135deg, #388E3C, #689F38)'
              }
            }}
          >
            Complete Profile Now
          </Button>
        </Box>
      </CardContent>
    </GlassCard>
  );
};

export default ProfileCompletionWidget;
