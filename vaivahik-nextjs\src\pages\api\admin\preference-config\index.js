// API endpoint for preference configuration
import { withAuth } from '@/utils/authHandler';
import { handleApiError } from '@/utils/errorHandler';
import axios from 'axios';

// Backend API URL - adjust this to your actual backend URL
const BACKEND_API_URL = 'http://localhost:3001/api';

// Main handler function
async function handler(req, res) {
  // Handle different HTTP methods
  try {
    switch (req.method) {
      case 'GET':
        return await getPreferenceConfig(req, res);
      case 'PUT':
        return await updatePreferenceConfig(req, res);
      case 'DELETE':
        return await deletePreferenceItem(req, res);
      default:
        return res.status(405).json({ success: false, message: 'Method not allowed' });
    }
  } catch (error) {
    return handleApiError(error, res, 'Preference configuration API');
  }
}

// Export the handler with authentication middleware
export default withAuth(handler, 'ADMIN');

// DELETE /api/admin/preference-config
async function deletePreferenceItem(req, res) {
  try {
    const { type, id } = req.query;

    // Validate the request
    if (!type || !id) {
      return res.status(400).json({ success: false, message: 'Type and ID are required' });
    }

    try {
      // Try to delete the item via the backend API
      const response = await axios.delete(`${BACKEND_API_URL}/admin/preference-config?type=${type}&id=${id}`);

      if (response.data && response.status === 200) {
        return res.status(200).json(response.data);
      }
    } catch (apiError) {
      console.warn(`Error deleting ${type} with ID ${id} via backend API:`, apiError.message);

      // If API call fails, return a meaningful error
      return res.status(500).json({
        success: false,
        message: `Failed to delete ${type}. Backend API connection failed.`,
        error: apiError.message,
        note: "The frontend is configured to use the backend API for deletions. Please ensure the backend server is running."
      });
    }
  } catch (error) {
    return handleApiError(error, res, 'Delete preference item');
  }
}

// GET /api/admin/preference-config
async function getPreferenceConfig(req, res) {
  try {
    // Check what data is requested
    const { type } = req.query;

    // Construct the API URL based on the type parameter
    const apiUrl = !type || type === 'all'
      ? `${BACKEND_API_URL}/admin/preference-config`
      : `${BACKEND_API_URL}/admin/preference-config?type=${type}`;

    try {
      // Fetch data from the backend API
      const response = await axios.get(apiUrl);

      // Return the response directly from the backend
      return res.status(200).json(response.data);
    } catch (apiError) {
      // Log the error
      console.error('Error fetching from backend API:', apiError.message);

      // Return a meaningful error message
      return res.status(500).json({
        success: false,
        message: 'Failed to fetch preference configuration from backend API.',
        error: apiError.message,
        details: apiError.response?.data || 'No additional details available',
        note: "Please ensure the backend server is running and accessible."
      });
    }
  } catch (error) {
    return handleApiError(error, res, 'Get preference configuration');
  }
}

// PUT /api/admin/preference-config
async function updatePreferenceConfig(req, res) {
  try {
    const { type, data } = req.body;

    // Validate the request
    if (!type || !data) {
      return res.status(400).json({ success: false, message: 'Type and data are required' });
    }

    try {
      // Try to update data via the backend API
      const response = await axios.put(`${BACKEND_API_URL}/admin/preference-config`, {
        type,
        data
      });

      if (response.data && response.status === 200) {
        return res.status(200).json(response.data);
      }
    } catch (apiError) {
      console.warn(`Error updating ${type} via backend API:`, apiError.message);

      // If API call fails, return a meaningful error
      return res.status(500).json({
        success: false,
        message: `Failed to update ${type}. Backend API connection failed.`,
        error: apiError.message,
        note: "The frontend is configured to use the backend API for updates. Please ensure the backend server is running."
      });
    }
  } catch (error) {
    return handleApiError(error, res, 'Update preference configuration');
  }
}
