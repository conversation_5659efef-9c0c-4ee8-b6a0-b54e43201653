/**
 * Premium Plans Modal Component
 * Shows subscription plans and premium features
 */

import { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Box,
  Typography,
  Button,
  Card,
  CardContent,
  Grid,
  Chip,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Avatar,
  Divider,
  Alert,
  IconButton
} from '@mui/material';
import { styled } from '@mui/material/styles';
import {
  WorkspacePremium as PremiumIcon,
  Star as StarIcon,
  CheckCircle as CheckIcon,
  Close as CloseIcon,
  Diamond as DiamondIcon,
  Favorite as HeartIcon,
  Message as MessageIcon,
  Phone as PhoneIcon,
  Visibility as ViewIcon,
  Search as SearchIcon,
  Security as SecurityIcon,
  Support as SupportIcon,
  TrendingUp as TrendingIcon
} from '@mui/icons-material';

const PremiumCard = styled(Card)(({ theme, selected, popular }) => ({
  background: selected 
    ? 'linear-gradient(135deg, rgba(255, 215, 0, 0.1), rgba(255, 140, 0, 0.1))'
    : 'rgba(255, 255, 255, 0.9)',
  border: selected 
    ? '2px solid #FFD700'
    : popular 
      ? '2px solid #FF69B4'
      : '1px solid rgba(255, 255, 255, 0.3)',
  borderRadius: 16,
  transition: 'all 0.3s ease',
  cursor: 'pointer',
  position: 'relative',
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: '0 12px 32px rgba(0, 0, 0, 0.15)'
  }
}));

const PopularBadge = styled(Chip)(({ theme }) => ({
  position: 'absolute',
  top: -10,
  left: '50%',
  transform: 'translateX(-50%)',
  background: 'linear-gradient(135deg, #FF69B4, #FF1493)',
  color: 'white',
  fontWeight: 600,
  zIndex: 1
}));

const PremiumPlansModal = ({ open, onClose, currentPlan = null }) => {
  const [selectedPlan, setSelectedPlan] = useState(null);
  const [plans, setPlans] = useState([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (open) {
      fetchPlans();
    }
  }, [open]);

  const fetchPlans = async () => {
    try {
      // Mock data - replace with actual API call
      setPlans([
        {
          id: 'monthly',
          name: 'Monthly Premium',
          price: 999,
          originalPrice: 1299,
          duration: '1 Month',
          savings: null,
          popular: false,
          features: [
            'Unlimited profile views',
            'Direct contact details access',
            'Advanced search filters',
            'Priority in search results',
            'Basic chat features',
            'Email support'
          ],
          icon: <PremiumIcon />,
          color: '#2196F3'
        },
        {
          id: 'quarterly',
          name: 'Quarterly Premium',
          price: 2499,
          originalPrice: 3897,
          duration: '3 Months',
          savings: '36% OFF',
          popular: true,
          features: [
            'All Monthly features',
            'Unlimited messaging',
            'Voice calling feature',
            'Profile boost every month',
            'Priority customer support',
            'Advanced matching algorithm'
          ],
          icon: <StarIcon />,
          color: '#FF69B4'
        },
        {
          id: 'annual',
          name: 'Annual Premium',
          price: 7999,
          originalPrice: 15588,
          duration: '12 Months',
          savings: '49% OFF',
          popular: false,
          features: [
            'All Quarterly features',
            'Video calling feature',
            'Dedicated relationship manager',
            'Profile verification priority',
            'Exclusive premium events',
            'Lifetime customer support'
          ],
          icon: <DiamondIcon />,
          color: '#9C27B0'
        }
      ]);
    } catch (error) {
      console.error('Error fetching plans:', error);
    }
  };

  const handlePlanSelect = (plan) => {
    setSelectedPlan(plan);
  };

  const handlePurchase = () => {
    if (!selectedPlan) return;
    
    // Redirect to payment page
    window.location.href = `/payment/subscription/${selectedPlan.id}`;
  };

  const premiumBenefits = [
    {
      icon: <ViewIcon />,
      title: 'Unlimited Profile Views',
      description: 'View as many profiles as you want without any restrictions'
    },
    {
      icon: <MessageIcon />,
      title: 'Unlimited Messaging',
      description: 'Chat with all your matches without any message limits'
    },
    {
      icon: <PhoneIcon />,
      title: 'Voice & Video Calls',
      description: 'Connect with matches through voice and video calls'
    },
    {
      icon: <SearchIcon />,
      title: 'Advanced Search',
      description: 'Use advanced filters to find your perfect match'
    },
    {
      icon: <TrendingIcon />,
      title: 'Priority Visibility',
      description: 'Your profile gets priority placement in search results'
    },
    {
      icon: <SecurityIcon />,
      title: 'Verified Badge',
      description: 'Get a verified badge to increase profile credibility'
    }
  ];

  return (
    <Dialog 
      open={open} 
      onClose={onClose} 
      maxWidth="lg" 
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 3,
          background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(255, 240, 245, 0.95))',
          backdropFilter: 'blur(20px)'
        }
      }}
    >
      <DialogTitle sx={{ textAlign: 'center', pb: 1 }}>
        <IconButton
          onClick={onClose}
          sx={{ position: 'absolute', right: 8, top: 8 }}
        >
          <CloseIcon />
        </IconButton>
        <PremiumIcon sx={{ color: '#FFD700', fontSize: 48, mb: 1 }} />
        <Typography variant="h4" fontWeight="700" gutterBottom>
          Upgrade to Premium
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Unlock all features and find your perfect match faster
        </Typography>
      </DialogTitle>

      <DialogContent sx={{ px: 3 }}>
        {/* Current Plan Alert */}
        {currentPlan && (
          <Alert severity="info" sx={{ mb: 3, borderRadius: 2 }}>
            <Typography variant="body2">
              You currently have the <strong>{currentPlan}</strong> plan. Upgrade to unlock more features!
            </Typography>
          </Alert>
        )}

        {/* Premium Benefits */}
        <Box sx={{ mb: 4 }}>
          <Typography variant="h6" fontWeight="600" gutterBottom sx={{ textAlign: 'center' }}>
            Why Choose Premium?
          </Typography>
          <Grid container spacing={2}>
            {premiumBenefits.map((benefit, index) => (
              <Grid item xs={12} sm={6} md={4} key={index}>
                <Box sx={{ textAlign: 'center', p: 2 }}>
                  <Avatar
                    sx={{
                      backgroundColor: 'rgba(255, 105, 180, 0.1)',
                      color: '#FF69B4',
                      width: 48,
                      height: 48,
                      mx: 'auto',
                      mb: 1
                    }}
                  >
                    {benefit.icon}
                  </Avatar>
                  <Typography variant="subtitle2" fontWeight="600" gutterBottom>
                    {benefit.title}
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ fontSize: '0.8rem' }}>
                    {benefit.description}
                  </Typography>
                </Box>
              </Grid>
            ))}
          </Grid>
        </Box>

        <Divider sx={{ my: 3 }} />

        {/* Subscription Plans */}
        <Typography variant="h6" fontWeight="600" gutterBottom sx={{ textAlign: 'center', mb: 3 }}>
          Choose Your Plan
        </Typography>
        
        <Grid container spacing={3}>
          {plans.map((plan) => (
            <Grid item xs={12} md={4} key={plan.id}>
              <PremiumCard
                selected={selectedPlan?.id === plan.id}
                popular={plan.popular}
                onClick={() => handlePlanSelect(plan)}
              >
                {plan.popular && <PopularBadge label="Most Popular" />}
                <CardContent sx={{ p: 3, textAlign: 'center' }}>
                  <Avatar
                    sx={{
                      backgroundColor: plan.color,
                      color: 'white',
                      width: 56,
                      height: 56,
                      mx: 'auto',
                      mb: 2
                    }}
                  >
                    {plan.icon}
                  </Avatar>
                  
                  <Typography variant="h6" fontWeight="600" gutterBottom>
                    {plan.name}
                  </Typography>
                  
                  <Box sx={{ mb: 2 }}>
                    <Typography variant="h4" fontWeight="700" color={plan.color}>
                      ₹{plan.price}
                    </Typography>
                    {plan.originalPrice && (
                      <Typography
                        variant="body2"
                        color="text.secondary"
                        sx={{ textDecoration: 'line-through' }}
                      >
                        ₹{plan.originalPrice}
                      </Typography>
                    )}
                    <Typography variant="body2" color="text.secondary">
                      for {plan.duration}
                    </Typography>
                    {plan.savings && (
                      <Chip
                        label={plan.savings}
                        size="small"
                        sx={{
                          mt: 1,
                          background: 'linear-gradient(135deg, #4CAF50, #8BC34A)',
                          color: 'white',
                          fontWeight: 600
                        }}
                      />
                    )}
                  </Box>
                  
                  <List dense sx={{ textAlign: 'left' }}>
                    {plan.features.map((feature, index) => (
                      <ListItem key={index} sx={{ py: 0.5, px: 0 }}>
                        <ListItemIcon sx={{ minWidth: 24 }}>
                          <CheckIcon sx={{ fontSize: 16, color: plan.color }} />
                        </ListItemIcon>
                        <ListItemText
                          primary={
                            <Typography variant="body2" sx={{ fontSize: '0.85rem' }}>
                              {feature}
                            </Typography>
                          }
                        />
                      </ListItem>
                    ))}
                  </List>
                </CardContent>
              </PremiumCard>
            </Grid>
          ))}
        </Grid>

        {/* Money Back Guarantee */}
        <Alert severity="success" sx={{ mt: 3, borderRadius: 2 }}>
          <Typography variant="body2" fontWeight="600">
            💰 30-Day Money Back Guarantee
          </Typography>
          <Typography variant="body2">
            Not satisfied? Get a full refund within 30 days, no questions asked.
          </Typography>
        </Alert>
      </DialogContent>

      <DialogActions sx={{ p: 3, pt: 0 }}>
        <Button onClick={onClose} size="large">
          Maybe Later
        </Button>
        <Button
          variant="contained"
          size="large"
          disabled={!selectedPlan}
          onClick={handlePurchase}
          sx={{
            background: selectedPlan 
              ? `linear-gradient(135deg, ${selectedPlan.color}, ${selectedPlan.color}dd)`
              : 'linear-gradient(135deg, #FF69B4, #FF1493)',
            px: 4,
            py: 1.5,
            fontSize: '1.1rem',
            fontWeight: 600,
            '&:hover': {
              background: selectedPlan 
                ? `linear-gradient(135deg, ${selectedPlan.color}dd, ${selectedPlan.color}bb)`
                : 'linear-gradient(135deg, #FF1493, #DC143C)'
            }
          }}
        >
          {selectedPlan ? `Upgrade to ${selectedPlan.name}` : 'Select a Plan'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default PremiumPlansModal;
