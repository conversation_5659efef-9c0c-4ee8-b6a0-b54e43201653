/**
 * Biodata Templates Component for User Dashboard
 * Shows available biodata templates and purchase options
 */

import { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  Grid,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Avatar,
  IconButton,
  Tooltip,
  Alert,
  CardMedia,
  CardActions
} from '@mui/material';
import { styled } from '@mui/material/styles';
import {
  Description as BiodataIcon,
  Download as DownloadIcon,
  Visibility as PreviewIcon,
  ShoppingCart as PurchaseIcon,
  Star as StarIcon,
  Lock as LockIcon,
  CheckCircle as CheckIcon,
  Male as MaleIcon,
  Female as FemaleIcon,
  Palette as DesignIcon
} from '@mui/icons-material';

const GlassCard = styled(Card)(({ theme }) => ({
  background: 'rgba(255, 255, 255, 0.95)',
  backdropFilter: 'blur(20px)',
  border: '1px solid rgba(255, 255, 255, 0.2)',
  borderRadius: 24,
  boxShadow: `
    0 20px 40px rgba(0, 0, 0, 0.1),
    0 8px 32px rgba(255, 182, 193, 0.2)
  `,
  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
  position: 'relative',
  overflow: 'hidden',
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: 4,
    background: 'linear-gradient(90deg, #9C27B0, #E91E63, #FF5722)',
    borderRadius: '24px 24px 0 0'
  }
}));

const TemplateCard = styled(Card)(({ theme }) => ({
  background: 'rgba(255, 255, 255, 0.9)',
  backdropFilter: 'blur(10px)',
  border: '1px solid rgba(255, 255, 255, 0.3)',
  borderRadius: 16,
  transition: 'all 0.3s ease',
  cursor: 'pointer',
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: '0 12px 32px rgba(0, 0, 0, 0.15)'
  }
}));

const BiodataTemplates = ({ userId, userGender = 'male' }) => {
  const [templates, setTemplates] = useState([]);
  const [selectedTemplate, setSelectedTemplate] = useState(null);
  const [showPreviewDialog, setShowPreviewDialog] = useState(false);
  const [showPurchaseDialog, setShowPurchaseDialog] = useState(false);
  const [purchasedTemplates, setPurchasedTemplates] = useState([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    fetchTemplates();
    fetchPurchasedTemplates();
  }, [userId, userGender]);

  const fetchTemplates = async () => {
    try {
      // Mock data - replace with actual API call
      const mockTemplates = [
        {
          id: 1,
          name: 'Classic Maratha',
          description: 'Traditional Maratha biodata with elegant design',
          thumbnail: '/api/placeholder/200/280',
          genderOrientation: 'male',
          category: 'free',
          price: 0,
          isPremium: false,
          features: ['Traditional Design', 'Maratha Symbols', 'Family Tree Section']
        },
        {
          id: 2,
          name: 'Royal Heritage',
          description: 'Premium design with royal Maratha heritage elements',
          thumbnail: '/api/placeholder/200/280',
          genderOrientation: 'male',
          category: 'premium',
          price: 299,
          isPremium: true,
          features: ['Royal Design', 'Gold Accents', 'Premium Layout', 'Horoscope Section']
        },
        {
          id: 3,
          name: 'Modern Elegance',
          description: 'Contemporary design for modern Maratha families',
          thumbnail: '/api/placeholder/200/280',
          genderOrientation: 'female',
          category: 'free',
          price: 0,
          isPremium: false,
          features: ['Modern Layout', 'Clean Design', 'Photo Gallery']
        },
        {
          id: 4,
          name: 'Floral Beauty',
          description: 'Beautiful floral design for female profiles',
          thumbnail: '/api/placeholder/200/280',
          genderOrientation: 'female',
          category: 'premium',
          price: 299,
          isPremium: true,
          features: ['Floral Elements', 'Elegant Typography', 'Premium Quality', 'Custom Colors']
        }
      ];

      // Filter templates based on user gender
      const filteredTemplates = mockTemplates.filter(
        template => template.genderOrientation === userGender || template.genderOrientation === 'neutral'
      );

      setTemplates(filteredTemplates);
    } catch (error) {
      console.error('Error fetching templates:', error);
    }
  };

  const fetchPurchasedTemplates = async () => {
    try {
      // Mock data - replace with actual API call
      setPurchasedTemplates([1, 3]); // User has purchased templates 1 and 3
    } catch (error) {
      console.error('Error fetching purchased templates:', error);
    }
  };

  const handlePreview = (template) => {
    setSelectedTemplate(template);
    setShowPreviewDialog(true);
  };

  const handlePurchase = (template) => {
    setSelectedTemplate(template);
    setShowPurchaseDialog(true);
  };

  const handleDownload = (template) => {
    // Implement download logic
    console.log('Downloading template:', template.id);
  };

  const isTemplatePurchased = (templateId) => {
    return purchasedTemplates.includes(templateId);
  };

  return (
    <GlassCard>
      <CardContent sx={{ p: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
          <BiodataIcon sx={{ color: '#9C27B0', mr: 2, fontSize: 28 }} />
          <Typography variant="h6" fontWeight="600">
            Biodata Templates
          </Typography>
          <Chip
            label={`${userGender === 'male' ? 'Male' : 'Female'} Oriented`}
            size="small"
            icon={userGender === 'male' ? <MaleIcon /> : <FemaleIcon />}
            sx={{
              ml: 'auto',
              backgroundColor: userGender === 'male' ? 'rgba(33, 150, 243, 0.1)' : 'rgba(233, 30, 99, 0.1)',
              color: userGender === 'male' ? '#2196F3' : '#E91E63'
            }}
          />
        </Box>

        <Alert severity="info" sx={{ mb: 3, borderRadius: 2 }}>
          <Typography variant="body2">
            Create beautiful biodata with our professionally designed templates. All templates include "Shree Ganeshay Namah" header and service branding.
          </Typography>
        </Alert>

        <Grid container spacing={2}>
          {templates.map((template) => (
            <Grid item xs={12} sm={6} md={4} key={template.id}>
              <TemplateCard>
                <Box sx={{ position: 'relative' }}>
                  <CardMedia
                    component="img"
                    height="200"
                    image={template.thumbnail}
                    alt={template.name}
                    sx={{ objectFit: 'cover' }}
                  />
                  {template.isPremium && (
                    <Chip
                      label="Premium"
                      size="small"
                      icon={<StarIcon />}
                      sx={{
                        position: 'absolute',
                        top: 8,
                        right: 8,
                        background: 'linear-gradient(135deg, #FFD700, #FFA000)',
                        color: 'white',
                        fontWeight: 600
                      }}
                    />
                  )}
                  {isTemplatePurchased(template.id) && (
                    <CheckIcon
                      sx={{
                        position: 'absolute',
                        top: 8,
                        left: 8,
                        color: '#4CAF50',
                        backgroundColor: 'white',
                        borderRadius: '50%',
                        fontSize: 24
                      }}
                    />
                  )}
                </Box>
                <CardContent sx={{ p: 2 }}>
                  <Typography variant="h6" fontWeight="600" gutterBottom>
                    {template.name}
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                    {template.description}
                  </Typography>
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mb: 2 }}>
                    {template.features.slice(0, 2).map((feature, index) => (
                      <Chip
                        key={index}
                        label={feature}
                        size="small"
                        variant="outlined"
                        sx={{ fontSize: '0.7rem' }}
                      />
                    ))}
                  </Box>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Typography variant="h6" fontWeight="700" color="primary">
                      {template.price === 0 ? 'Free' : `₹${template.price}`}
                    </Typography>
                    <Box sx={{ display: 'flex', gap: 1 }}>
                      <Tooltip title="Preview">
                        <IconButton
                          size="small"
                          onClick={() => handlePreview(template)}
                          sx={{ color: '#2196F3' }}
                        >
                          <PreviewIcon />
                        </IconButton>
                      </Tooltip>
                      {isTemplatePurchased(template.id) ? (
                        <Tooltip title="Download">
                          <IconButton
                            size="small"
                            onClick={() => handleDownload(template)}
                            sx={{ color: '#4CAF50' }}
                          >
                            <DownloadIcon />
                          </IconButton>
                        </Tooltip>
                      ) : (
                        <Tooltip title={template.price === 0 ? 'Use Template' : 'Purchase'}>
                          <IconButton
                            size="small"
                            onClick={() => handlePurchase(template)}
                            sx={{ color: '#FF69B4' }}
                          >
                            {template.price === 0 ? <DownloadIcon /> : <PurchaseIcon />}
                          </IconButton>
                        </Tooltip>
                      )}
                    </Box>
                  </Box>
                </CardContent>
              </TemplateCard>
            </Grid>
          ))}
        </Grid>

        <Box sx={{ mt: 3, textAlign: 'center' }}>
          <Button
            variant="outlined"
            fullWidth
            startIcon={<DesignIcon />}
            onClick={() => window.location.href = '/biodata-templates'}
            sx={{
              borderColor: '#9C27B0',
              color: '#9C27B0',
              '&:hover': {
                borderColor: '#7B1FA2',
                backgroundColor: 'rgba(156, 39, 176, 0.1)'
              }
            }}
          >
            View All Templates
          </Button>
        </Box>
      </CardContent>

      {/* Preview Dialog */}
      <Dialog open={showPreviewDialog} onClose={() => setShowPreviewDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>
          Template Preview: {selectedTemplate?.name}
        </DialogTitle>
        <DialogContent>
          <Box sx={{ textAlign: 'center', p: 2 }}>
            <img
              src={selectedTemplate?.thumbnail}
              alt={selectedTemplate?.name}
              style={{ maxWidth: '100%', height: 'auto', borderRadius: 8 }}
            />
            <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
              {selectedTemplate?.description}
            </Typography>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowPreviewDialog(false)}>Close</Button>
          <Button
            variant="contained"
            onClick={() => {
              setShowPreviewDialog(false);
              handlePurchase(selectedTemplate);
            }}
            sx={{
              background: 'linear-gradient(135deg, #9C27B0, #E91E63)',
              '&:hover': {
                background: 'linear-gradient(135deg, #7B1FA2, #C2185B)'
              }
            }}
          >
            {selectedTemplate?.price === 0 ? 'Use Template' : `Purchase ₹${selectedTemplate?.price}`}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Purchase Dialog */}
      <Dialog open={showPurchaseDialog} onClose={() => setShowPurchaseDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>
          {selectedTemplate?.price === 0 ? 'Use Template' : 'Purchase Template'}
        </DialogTitle>
        <DialogContent>
          <Typography variant="body1" paragraph>
            {selectedTemplate?.price === 0 
              ? `You're about to use the "${selectedTemplate?.name}" template. This template is free and you can download it immediately.`
              : `You're about to purchase the "${selectedTemplate?.name}" template for ₹${selectedTemplate?.price}. This is a one-time purchase and you'll have lifetime access.`
            }
          </Typography>
          {selectedTemplate?.features && (
            <Box>
              <Typography variant="subtitle2" gutterBottom>Features included:</Typography>
              <ul>
                {selectedTemplate.features.map((feature, index) => (
                  <li key={index}>
                    <Typography variant="body2">{feature}</Typography>
                  </li>
                ))}
              </ul>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowPurchaseDialog(false)}>Cancel</Button>
          <Button
            variant="contained"
            sx={{
              background: 'linear-gradient(135deg, #9C27B0, #E91E63)',
              '&:hover': {
                background: 'linear-gradient(135deg, #7B1FA2, #C2185B)'
              }
            }}
          >
            {selectedTemplate?.price === 0 ? 'Download Now' : 'Proceed to Payment'}
          </Button>
        </DialogActions>
      </Dialog>
    </GlassCard>
  );
};

export default BiodataTemplates;
