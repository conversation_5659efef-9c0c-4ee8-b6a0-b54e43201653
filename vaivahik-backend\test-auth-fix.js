// Test script to verify auth middleware fixes
const express = require('express');
const { authenticateToken } = require('./src/middleware/auth.middleware');

const app = express();
app.use(express.json());

// Test route using the fixed auth middleware
app.get('/test-auth', authenticateToken, (req, res) => {
  res.json({
    success: true,
    message: 'Auth middleware working correctly',
    user: req.user
  });
});

// Test the privacy routes import
try {
  const privacyRoutes = require('./routes/privacy-routes.js');
  console.log('✅ Privacy routes imported successfully');
} catch (error) {
  console.error('❌ Privacy routes import failed:', error.message);
  process.exit(1);
}

// Test the contact routes import
try {
  const contactRoutes = require('./routes/contact-routes.js');
  console.log('✅ Contact routes imported successfully');
} catch (error) {
  console.error('❌ Contact routes import failed:', error.message);
  process.exit(1);
}

console.log('✅ All route imports successful - auth middleware fix is working!');
process.exit(0);
