// Quick test to verify server is responding
const http = require('http');

function testServer() {
  const options = {
    hostname: 'localhost',
    port: 8080,
    path: '/health',
    method: 'GET',
    timeout: 5000
  };

  const req = http.request(options, (res) => {
    console.log(`✅ Server responding! Status: ${res.statusCode}`);
    
    let data = '';
    res.on('data', (chunk) => {
      data += chunk;
    });
    
    res.on('end', () => {
      console.log('✅ Response:', data);
      console.log('🎉 Backend server is working correctly!');
      process.exit(0);
    });
  });

  req.on('error', (err) => {
    console.log(`❌ Server not responding: ${err.message}`);
    console.log('⏳ Server might still be starting up...');
    process.exit(1);
  });

  req.on('timeout', () => {
    console.log('❌ Request timeout');
    req.destroy();
    process.exit(1);
  });

  req.end();
}

console.log('🔍 Testing server at http://localhost:8080/health...');
testServer();
