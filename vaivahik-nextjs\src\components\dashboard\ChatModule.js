/**
 * Chat Module Component for User Dashboard
 * Shows recent chats and premium chat features
 */

import { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  Avatar,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  Badge,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  Divider
} from '@mui/material';
import { styled } from '@mui/material/styles';
import {
  Chat as ChatIcon,
  Message as MessageIcon,
  VideoCall as VideoIcon,
  Phone as PhoneIcon,
  Star as StarIcon,
  Lock as LockIcon,
  Send as SendIcon,
  MoreVert as MoreIcon,
  FiberManualRecord as OnlineIcon,
  WorkspacePremium as PremiumIcon
} from '@mui/icons-material';

const GlassCard = styled(Card)(({ theme }) => ({
  background: 'rgba(255, 255, 255, 0.95)',
  backdropFilter: 'blur(20px)',
  border: '1px solid rgba(255, 255, 255, 0.2)',
  borderRadius: 24,
  boxShadow: `
    0 20px 40px rgba(0, 0, 0, 0.1),
    0 8px 32px rgba(255, 182, 193, 0.2)
  `,
  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
  position: 'relative',
  overflow: 'hidden',
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: 4,
    background: 'linear-gradient(90deg, #4CAF50, #8BC34A, #CDDC39)',
    borderRadius: '24px 24px 0 0'
  }
}));

const ChatModule = ({ userId, isPremium = false }) => {
  const [recentChats, setRecentChats] = useState([]);
  const [showPremiumDialog, setShowPremiumDialog] = useState(false);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    fetchRecentChats();
  }, [userId]);

  const fetchRecentChats = async () => {
    try {
      // Mock data - replace with actual API call
      setRecentChats([
        {
          id: 1,
          name: 'Priya Sharma',
          avatar: '/api/placeholder/50/50',
          lastMessage: 'Thank you for your interest! I would love to know more about you.',
          timestamp: '2 min ago',
          unreadCount: 2,
          isOnline: true,
          isPremiumUser: true
        },
        {
          id: 2,
          name: 'Anita Patil',
          avatar: '/api/placeholder/50/50',
          lastMessage: 'Hi! I saw your profile and found it interesting.',
          timestamp: '1 hour ago',
          unreadCount: 0,
          isOnline: false,
          isPremiumUser: false
        },
        {
          id: 3,
          name: 'Kavya Desai',
          avatar: '/api/placeholder/50/50',
          lastMessage: 'Would you like to connect over a call?',
          timestamp: '3 hours ago',
          unreadCount: 1,
          isOnline: true,
          isPremiumUser: true
        }
      ]);
    } catch (error) {
      console.error('Error fetching chats:', error);
    }
  };

  const handleChatClick = (chatId) => {
    if (!isPremium) {
      setShowPremiumDialog(true);
      return;
    }
    // Navigate to chat
    window.location.href = `/chat/${chatId}`;
  };

  const handleCallClick = (chatId) => {
    if (!isPremium) {
      setShowPremiumDialog(true);
      return;
    }
    // Initiate call
    console.log('Initiating call with:', chatId);
  };

  const formatTimestamp = (timestamp) => {
    return timestamp;
  };

  return (
    <GlassCard>
      <CardContent sx={{ p: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
          <ChatIcon sx={{ color: '#4CAF50', mr: 2, fontSize: 28 }} />
          <Typography variant="h6" fontWeight="600">
            Recent Chats
          </Typography>
          {!isPremium && (
            <Chip
              label="Premium"
              size="small"
              icon={<PremiumIcon />}
              sx={{
                ml: 'auto',
                background: 'linear-gradient(135deg, #FFD700, #FFA000)',
                color: 'white',
                fontWeight: 600
              }}
            />
          )}
        </Box>

        {!isPremium && (
          <Alert 
            severity="info" 
            sx={{ mb: 3, borderRadius: 2 }}
            action={
              <Button
                size="small"
                variant="contained"
                onClick={() => setShowPremiumDialog(true)}
                sx={{
                  background: 'linear-gradient(135deg, #FF69B4, #FF1493)',
                  '&:hover': {
                    background: 'linear-gradient(135deg, #FF1493, #DC143C)'
                  }
                }}
              >
                Upgrade
              </Button>
            }
          >
            <Typography variant="body2">
              Upgrade to Premium to unlock unlimited messaging and calling features!
            </Typography>
          </Alert>
        )}

        <List sx={{ p: 0 }}>
          {recentChats.map((chat, index) => (
            <Box key={chat.id}>
              <ListItem
                sx={{
                  borderRadius: 2,
                  cursor: 'pointer',
                  '&:hover': {
                    backgroundColor: 'rgba(76, 175, 80, 0.05)'
                  },
                  opacity: !isPremium ? 0.6 : 1,
                  position: 'relative'
                }}
                onClick={() => handleChatClick(chat.id)}
              >
                <ListItemAvatar>
                  <Badge
                    overlap="circular"
                    anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
                    badgeContent={
                      chat.isOnline ? (
                        <OnlineIcon sx={{ color: '#4CAF50', fontSize: 12 }} />
                      ) : null
                    }
                  >
                    <Avatar src={chat.avatar} sx={{ width: 50, height: 50 }}>
                      {chat.name.charAt(0)}
                    </Avatar>
                  </Badge>
                </ListItemAvatar>
                <ListItemText
                  primary={
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Typography variant="subtitle2" fontWeight="600">
                        {chat.name}
                      </Typography>
                      {chat.isPremiumUser && (
                        <StarIcon sx={{ color: '#FFD700', fontSize: 16 }} />
                      )}
                    </Box>
                  }
                  secondary={
                    <Box>
                      <Typography
                        variant="body2"
                        color="text.secondary"
                        sx={{
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                          whiteSpace: 'nowrap',
                          maxWidth: '200px'
                        }}
                      >
                        {chat.lastMessage}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {formatTimestamp(chat.timestamp)}
                      </Typography>
                    </Box>
                  }
                />
                <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: 1 }}>
                  {chat.unreadCount > 0 && (
                    <Badge
                      badgeContent={chat.unreadCount}
                      color="primary"
                      sx={{
                        '& .MuiBadge-badge': {
                          background: 'linear-gradient(135deg, #FF69B4, #FF1493)',
                          color: 'white'
                        }
                      }}
                    />
                  )}
                  <Box sx={{ display: 'flex', gap: 0.5 }}>
                    <IconButton
                      size="small"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleChatClick(chat.id);
                      }}
                      sx={{ color: '#4CAF50' }}
                    >
                      <MessageIcon fontSize="small" />
                    </IconButton>
                    <IconButton
                      size="small"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleCallClick(chat.id);
                      }}
                      sx={{ color: '#2196F3' }}
                    >
                      <PhoneIcon fontSize="small" />
                    </IconButton>
                  </Box>
                </Box>
                {!isPremium && (
                  <LockIcon
                    sx={{
                      position: 'absolute',
                      top: '50%',
                      left: '50%',
                      transform: 'translate(-50%, -50%)',
                      color: '#FF69B4',
                      fontSize: 32,
                      backgroundColor: 'rgba(255, 255, 255, 0.9)',
                      borderRadius: '50%',
                      p: 1
                    }}
                  />
                )}
              </ListItem>
              {index < recentChats.length - 1 && <Divider />}
            </Box>
          ))}
        </List>

        <Box sx={{ mt: 3, textAlign: 'center' }}>
          <Button
            variant="outlined"
            fullWidth
            startIcon={<ChatIcon />}
            onClick={() => window.location.href = '/chat'}
            sx={{
              borderColor: '#4CAF50',
              color: '#4CAF50',
              '&:hover': {
                borderColor: '#388E3C',
                backgroundColor: 'rgba(76, 175, 80, 0.1)'
              }
            }}
          >
            View All Chats
          </Button>
        </Box>
      </CardContent>

      {/* Premium Dialog */}
      <Dialog open={showPremiumDialog} onClose={() => setShowPremiumDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle sx={{ textAlign: 'center' }}>
          <PremiumIcon sx={{ color: '#FFD700', fontSize: 48, mb: 1 }} />
          <Typography variant="h5" fontWeight="600">
            Unlock Premium Chat Features
          </Typography>
        </DialogTitle>
        <DialogContent>
          <Box sx={{ textAlign: 'center', mb: 3 }}>
            <Typography variant="body1" color="text.secondary" paragraph>
              Upgrade to Premium to enjoy unlimited messaging, voice calls, and video calls with your matches!
            </Typography>
          </Box>
          <List>
            {[
              'Unlimited messaging with all matches',
              'Voice and video calling features',
              'Read receipts and typing indicators',
              'Priority customer support',
              'Advanced chat filters and search'
            ].map((feature, index) => (
              <ListItem key={index} sx={{ py: 0.5 }}>
                <ListItemAvatar>
                  <Avatar sx={{ width: 24, height: 24, backgroundColor: '#4CAF50' }}>
                    <StarIcon sx={{ fontSize: 14, color: 'white' }} />
                  </Avatar>
                </ListItemAvatar>
                <ListItemText
                  primary={
                    <Typography variant="body2">
                      {feature}
                    </Typography>
                  }
                />
              </ListItem>
            ))}
          </List>
        </DialogContent>
        <DialogActions sx={{ p: 3, pt: 0 }}>
          <Button onClick={() => setShowPremiumDialog(false)}>
            Maybe Later
          </Button>
          <Button
            variant="contained"
            size="large"
            sx={{
              background: 'linear-gradient(135deg, #FF69B4, #FF1493)',
              px: 4,
              '&:hover': {
                background: 'linear-gradient(135deg, #FF1493, #DC143C)'
              }
            }}
            onClick={() => window.location.href = '/premium-plans'}
          >
            Upgrade Now
          </Button>
        </DialogActions>
      </Dialog>
    </GlassCard>
  );
};

export default ChatModule;
