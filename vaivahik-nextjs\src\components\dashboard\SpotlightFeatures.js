/**
 * Spotlight Features Component for User Dashboard
 * Shows spotlight options and current spotlight status
 */

import { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  Grid,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Avatar,
  LinearProgress,
  Alert,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider
} from '@mui/material';
import { styled } from '@mui/material/styles';
import {
  FlashOn as SpotlightIcon,
  Star as StarIcon,
  Visibility as ViewIcon,
  TrendingUp as TrendingIcon,
  Schedule as ScheduleIcon,
  CheckCircle as CheckIcon,
  ShoppingCart as PurchaseIcon,
  LocalFireDepartment as FireIcon,
  Diamond as DiamondIcon,
  Rocket as RocketIcon
} from '@mui/icons-material';

const GlassCard = styled(Card)(({ theme }) => ({
  background: 'rgba(255, 255, 255, 0.95)',
  backdropFilter: 'blur(20px)',
  border: '1px solid rgba(255, 255, 255, 0.2)',
  borderRadius: 24,
  boxShadow: `
    0 20px 40px rgba(0, 0, 0, 0.1),
    0 8px 32px rgba(255, 182, 193, 0.2)
  `,
  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
  position: 'relative',
  overflow: 'hidden',
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: 4,
    background: 'linear-gradient(90deg, #FF6B35, #F7931E, #FFD700)',
    borderRadius: '24px 24px 0 0'
  }
}));

const SpotlightCard = styled(Card)(({ theme }) => ({
  background: 'linear-gradient(135deg, rgba(255, 215, 0, 0.1), rgba(255, 140, 0, 0.1))',
  border: '2px solid rgba(255, 215, 0, 0.3)',
  borderRadius: 16,
  transition: 'all 0.3s ease',
  cursor: 'pointer',
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: '0 12px 32px rgba(255, 215, 0, 0.3)',
    borderColor: 'rgba(255, 215, 0, 0.5)'
  }
}));

const SpotlightFeatures = ({ userId, currentSpotlight = null }) => {
  const [spotlightOptions, setSpotlightOptions] = useState([]);
  const [selectedSpotlight, setSelectedSpotlight] = useState(null);
  const [showPurchaseDialog, setShowPurchaseDialog] = useState(false);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    fetchSpotlightOptions();
  }, [userId]);

  const fetchSpotlightOptions = async () => {
    try {
      // Mock data - replace with actual API call
      setSpotlightOptions([
        {
          id: 1,
          name: 'Basic Spotlight',
          duration: '24 hours',
          price: 199,
          description: 'Boost your profile visibility for 24 hours',
          features: [
            '3x more profile views',
            'Priority in search results',
            'Featured in spotlight section'
          ],
          icon: <SpotlightIcon />,
          color: '#FF6B35'
        },
        {
          id: 2,
          name: 'Premium Spotlight',
          duration: '7 days',
          price: 999,
          description: 'Premium visibility boost for a full week',
          features: [
            '5x more profile views',
            'Top priority in search',
            'Featured in spotlight section',
            'Special badge on profile',
            'Push notifications to matches'
          ],
          icon: <StarIcon />,
          color: '#FFD700',
          popular: true
        },
        {
          id: 3,
          name: 'Super Spotlight',
          duration: '30 days',
          price: 2999,
          description: 'Maximum visibility for an entire month',
          features: [
            '10x more profile views',
            'Highest priority in search',
            'Featured in all sections',
            'Premium badge on profile',
            'Daily push notifications',
            'Dedicated relationship manager'
          ],
          icon: <DiamondIcon />,
          color: '#9C27B0'
        }
      ]);
    } catch (error) {
      console.error('Error fetching spotlight options:', error);
    }
  };

  const handlePurchase = (spotlight) => {
    setSelectedSpotlight(spotlight);
    setShowPurchaseDialog(true);
  };

  const getRemainingTime = () => {
    if (!currentSpotlight) return null;
    // Calculate remaining time logic here
    return '2 days 14 hours';
  };

  const getSpotlightProgress = () => {
    if (!currentSpotlight) return 0;
    // Calculate progress logic here
    return 65;
  };

  return (
    <GlassCard>
      <CardContent sx={{ p: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
          <SpotlightIcon sx={{ color: '#FF6B35', mr: 2, fontSize: 28 }} />
          <Typography variant="h6" fontWeight="600">
            Spotlight Features
          </Typography>
          {currentSpotlight && (
            <Chip
              label="Active"
              size="small"
              icon={<FireIcon />}
              sx={{
                ml: 'auto',
                background: 'linear-gradient(135deg, #FF6B35, #F7931E)',
                color: 'white',
                fontWeight: 600
              }}
            />
          )}
        </Box>

        {currentSpotlight ? (
          <Box sx={{ mb: 3 }}>
            <Alert 
              severity="success" 
              sx={{ mb: 2, borderRadius: 2 }}
              icon={<FireIcon />}
            >
              <Typography variant="body2" fontWeight="600">
                Your profile is currently in the spotlight! 🔥
              </Typography>
            </Alert>
            
            <Box sx={{ 
              p: 2, 
              borderRadius: 2, 
              background: 'linear-gradient(135deg, rgba(255, 107, 53, 0.1), rgba(247, 147, 30, 0.1))',
              border: '1px solid rgba(255, 107, 53, 0.2)'
            }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                <Typography variant="body2" fontWeight="600">
                  {currentSpotlight.name}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {getRemainingTime()} remaining
                </Typography>
              </Box>
              <LinearProgress
                variant="determinate"
                value={getSpotlightProgress()}
                sx={{
                  height: 8,
                  borderRadius: 4,
                  backgroundColor: 'rgba(255, 107, 53, 0.1)',
                  '& .MuiLinearProgress-bar': {
                    background: 'linear-gradient(90deg, #FF6B35, #F7931E)',
                    borderRadius: 4
                  }
                }}
              />
              <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
                Your profile has received 3.2x more views during this spotlight period!
              </Typography>
            </Box>
          </Box>
        ) : (
          <Alert severity="info" sx={{ mb: 3, borderRadius: 2 }}>
            <Typography variant="body2">
              Boost your profile visibility and get up to 10x more views with our spotlight features!
            </Typography>
          </Alert>
        )}

        <Grid container spacing={2}>
          {spotlightOptions.map((option) => (
            <Grid item xs={12} key={option.id}>
              <SpotlightCard onClick={() => handlePurchase(option)}>
                <CardContent sx={{ p: 2 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <Avatar
                      sx={{
                        backgroundColor: option.color,
                        color: 'white',
                        mr: 2,
                        width: 48,
                        height: 48
                      }}
                    >
                      {option.icon}
                    </Avatar>
                    <Box sx={{ flex: 1 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Typography variant="h6" fontWeight="600">
                          {option.name}
                        </Typography>
                        {option.popular && (
                          <Chip
                            label="Most Popular"
                            size="small"
                            sx={{
                              background: 'linear-gradient(135deg, #FF6B35, #F7931E)',
                              color: 'white',
                              fontWeight: 600,
                              fontSize: '0.7rem'
                            }}
                          />
                        )}
                      </Box>
                      <Typography variant="body2" color="text.secondary">
                        {option.description}
                      </Typography>
                    </Box>
                    <Box sx={{ textAlign: 'right' }}>
                      <Typography variant="h6" fontWeight="700" color={option.color}>
                        ₹{option.price}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        for {option.duration}
                      </Typography>
                    </Box>
                  </Box>
                  
                  <List dense sx={{ p: 0 }}>
                    {option.features.map((feature, index) => (
                      <ListItem key={index} sx={{ py: 0, px: 0 }}>
                        <ListItemIcon sx={{ minWidth: 24 }}>
                          <CheckIcon sx={{ fontSize: 16, color: option.color }} />
                        </ListItemIcon>
                        <ListItemText
                          primary={
                            <Typography variant="body2">
                              {feature}
                            </Typography>
                          }
                        />
                      </ListItem>
                    ))}
                  </List>
                </CardContent>
              </SpotlightCard>
            </Grid>
          ))}
        </Grid>

        <Box sx={{ mt: 3, textAlign: 'center' }}>
          <Button
            variant="outlined"
            fullWidth
            startIcon={<RocketIcon />}
            onClick={() => window.location.href = '/spotlight'}
            sx={{
              borderColor: '#FF6B35',
              color: '#FF6B35',
              '&:hover': {
                borderColor: '#E55A2B',
                backgroundColor: 'rgba(255, 107, 53, 0.1)'
              }
            }}
          >
            View Spotlight Analytics
          </Button>
        </Box>
      </CardContent>

      {/* Purchase Dialog */}
      <Dialog open={showPurchaseDialog} onClose={() => setShowPurchaseDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle sx={{ textAlign: 'center' }}>
          <Avatar
            sx={{
              backgroundColor: selectedSpotlight?.color,
              color: 'white',
              width: 64,
              height: 64,
              mx: 'auto',
              mb: 2
            }}
          >
            {selectedSpotlight?.icon}
          </Avatar>
          <Typography variant="h5" fontWeight="600">
            Purchase {selectedSpotlight?.name}
          </Typography>
        </DialogTitle>
        <DialogContent>
          <Box sx={{ textAlign: 'center', mb: 3 }}>
            <Typography variant="h4" fontWeight="700" color={selectedSpotlight?.color} gutterBottom>
              ₹{selectedSpotlight?.price}
            </Typography>
            <Typography variant="body1" color="text.secondary">
              for {selectedSpotlight?.duration}
            </Typography>
          </Box>
          
          <Typography variant="body1" paragraph>
            {selectedSpotlight?.description}
          </Typography>
          
          <Typography variant="subtitle2" gutterBottom>
            What you'll get:
          </Typography>
          <List>
            {selectedSpotlight?.features.map((feature, index) => (
              <ListItem key={index} sx={{ py: 0.5 }}>
                <ListItemIcon>
                  <CheckIcon sx={{ color: selectedSpotlight?.color }} />
                </ListItemIcon>
                <ListItemText
                  primary={
                    <Typography variant="body2">
                      {feature}
                    </Typography>
                  }
                />
              </ListItem>
            ))}
          </List>
          
          <Alert severity="info" sx={{ mt: 2, borderRadius: 2 }}>
            <Typography variant="body2">
              Your spotlight will activate immediately after payment confirmation.
            </Typography>
          </Alert>
        </DialogContent>
        <DialogActions sx={{ p: 3, pt: 0 }}>
          <Button onClick={() => setShowPurchaseDialog(false)}>
            Cancel
          </Button>
          <Button
            variant="contained"
            size="large"
            startIcon={<PurchaseIcon />}
            sx={{
              background: `linear-gradient(135deg, ${selectedSpotlight?.color}, ${selectedSpotlight?.color}dd)`,
              px: 4,
              '&:hover': {
                background: `linear-gradient(135deg, ${selectedSpotlight?.color}dd, ${selectedSpotlight?.color}bb)`
              }
            }}
            onClick={() => window.location.href = `/payment/spotlight/${selectedSpotlight?.id}`}
          >
            Purchase Now
          </Button>
        </DialogActions>
      </Dialog>
    </GlassCard>
  );
};

export default SpotlightFeatures;
