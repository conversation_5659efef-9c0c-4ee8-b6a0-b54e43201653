/**
 * Verification Queue Component for User Dashboard
 * Shows user's verification status and pending verifications
 */

import { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  Chip,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Avatar,
  LinearProgress,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  Tooltip
} from '@mui/material';
import { styled } from '@mui/material/styles';
import {
  Verified as VerifiedIcon,
  Schedule as PendingIcon,
  CheckCircle as CheckIcon,
  Cancel as CancelIcon,
  PhotoCamera as PhotoIcon,
  Description as DocumentIcon,
  Phone as PhoneIcon,
  Email as EmailIcon,
  Upload as UploadIcon,
  Info as InfoIcon
} from '@mui/icons-material';

const GlassCard = styled(Card)(({ theme }) => ({
  background: 'rgba(255, 255, 255, 0.95)',
  backdropFilter: 'blur(20px)',
  border: '1px solid rgba(255, 255, 255, 0.2)',
  borderRadius: 24,
  boxShadow: `
    0 20px 40px rgba(0, 0, 0, 0.1),
    0 8px 32px rgba(255, 182, 193, 0.2)
  `,
  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
  position: 'relative',
  overflow: 'hidden',
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: 4,
    background: 'linear-gradient(90deg, #FF69B4, #FFB6C1, #FFC0CB)',
    borderRadius: '24px 24px 0 0'
  }
}));

const VerificationQueue = ({ userId }) => {
  const [verificationStatus, setVerificationStatus] = useState({
    overall: 'PENDING',
    photo: 'VERIFIED',
    document: 'PENDING',
    phone: 'VERIFIED',
    email: 'VERIFIED'
  });
  const [showUploadDialog, setShowUploadDialog] = useState(false);
  const [uploadType, setUploadType] = useState('');
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    fetchVerificationStatus();
  }, [userId]);

  const fetchVerificationStatus = async () => {
    try {
      // Mock data - replace with actual API call
      setVerificationStatus({
        overall: 'PENDING',
        photo: 'VERIFIED',
        document: 'PENDING',
        phone: 'VERIFIED',
        email: 'VERIFIED'
      });
    } catch (error) {
      console.error('Error fetching verification status:', error);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'VERIFIED': return '#4CAF50';
      case 'PENDING': return '#FF9800';
      case 'REJECTED': return '#F44336';
      default: return '#9E9E9E';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'VERIFIED': return <CheckIcon />;
      case 'PENDING': return <PendingIcon />;
      case 'REJECTED': return <CancelIcon />;
      default: return <InfoIcon />;
    }
  };

  const handleUpload = (type) => {
    setUploadType(type);
    setShowUploadDialog(true);
  };

  const verificationItems = [
    {
      id: 'photo',
      label: 'Profile Photo',
      icon: <PhotoIcon />,
      status: verificationStatus.photo,
      description: 'Clear face photo for identity verification'
    },
    {
      id: 'document',
      label: 'ID Document',
      icon: <DocumentIcon />,
      status: verificationStatus.document,
      description: 'Government ID for identity verification'
    },
    {
      id: 'phone',
      label: 'Phone Number',
      icon: <PhoneIcon />,
      status: verificationStatus.phone,
      description: 'Mobile number verification via OTP'
    },
    {
      id: 'email',
      label: 'Email Address',
      icon: <EmailIcon />,
      status: verificationStatus.email,
      description: 'Email verification via link'
    }
  ];

  const completedCount = verificationItems.filter(item => item.status === 'VERIFIED').length;
  const progressPercentage = (completedCount / verificationItems.length) * 100;

  return (
    <GlassCard>
      <CardContent sx={{ p: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
          <VerifiedIcon sx={{ color: '#FF69B4', mr: 2, fontSize: 28 }} />
          <Typography variant="h6" fontWeight="600">
            Verification Status
          </Typography>
          <Chip
            label={verificationStatus.overall}
            color={verificationStatus.overall === 'VERIFIED' ? 'success' : 'warning'}
            size="small"
            sx={{ ml: 'auto' }}
          />
        </Box>

        <Box sx={{ mb: 3 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
            <Typography variant="body2" color="text.secondary">
              Verification Progress
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {completedCount}/{verificationItems.length} Complete
            </Typography>
          </Box>
          <LinearProgress
            variant="determinate"
            value={progressPercentage}
            sx={{
              height: 8,
              borderRadius: 4,
              backgroundColor: 'rgba(255, 105, 180, 0.1)',
              '& .MuiLinearProgress-bar': {
                background: 'linear-gradient(90deg, #FF69B4, #FF1493)',
                borderRadius: 4
              }
            }}
          />
        </Box>

        {progressPercentage < 100 && (
          <Alert severity="info" sx={{ mb: 3, borderRadius: 2 }}>
            <Typography variant="body2">
              Complete your verification to unlock premium features and increase profile visibility by 300%!
            </Typography>
          </Alert>
        )}

        <List sx={{ p: 0 }}>
          {verificationItems.map((item) => (
            <ListItem
              key={item.id}
              sx={{
                borderRadius: 2,
                mb: 1,
                backgroundColor: item.status === 'VERIFIED' ? 'rgba(76, 175, 80, 0.05)' : 'rgba(255, 152, 0, 0.05)',
                border: `1px solid ${item.status === 'VERIFIED' ? 'rgba(76, 175, 80, 0.2)' : 'rgba(255, 152, 0, 0.2)'}`
              }}
            >
              <ListItemIcon>
                <Avatar
                  sx={{
                    width: 40,
                    height: 40,
                    backgroundColor: getStatusColor(item.status),
                    color: 'white'
                  }}
                >
                  {item.icon}
                </Avatar>
              </ListItemIcon>
              <ListItemText
                primary={
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Typography variant="subtitle2" fontWeight="600">
                      {item.label}
                    </Typography>
                    {getStatusIcon(item.status)}
                  </Box>
                }
                secondary={item.description}
              />
              {item.status !== 'VERIFIED' && (
                <Button
                  size="small"
                  variant="outlined"
                  startIcon={<UploadIcon />}
                  onClick={() => handleUpload(item.id)}
                  sx={{
                    borderColor: '#FF69B4',
                    color: '#FF69B4',
                    '&:hover': {
                      borderColor: '#FF1493',
                      backgroundColor: 'rgba(255, 105, 180, 0.1)'
                    }
                  }}
                >
                  Upload
                </Button>
              )}
            </ListItem>
          ))}
        </List>
      </CardContent>

      {/* Upload Dialog */}
      <Dialog open={showUploadDialog} onClose={() => setShowUploadDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>
          Upload {uploadType === 'photo' ? 'Profile Photo' : uploadType === 'document' ? 'ID Document' : 'Verification File'}
        </DialogTitle>
        <DialogContent>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
            Please upload a clear, high-quality image for verification. Accepted formats: JPG, PNG, PDF
          </Typography>
          <Box
            sx={{
              border: '2px dashed #FF69B4',
              borderRadius: 2,
              p: 4,
              textAlign: 'center',
              cursor: 'pointer',
              '&:hover': {
                backgroundColor: 'rgba(255, 105, 180, 0.05)'
              }
            }}
          >
            <UploadIcon sx={{ fontSize: 48, color: '#FF69B4', mb: 2 }} />
            <Typography variant="h6" gutterBottom>
              Click to upload or drag and drop
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Maximum file size: 5MB
            </Typography>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowUploadDialog(false)}>Cancel</Button>
          <Button
            variant="contained"
            sx={{
              background: 'linear-gradient(135deg, #FF69B4, #FF1493)',
              '&:hover': {
                background: 'linear-gradient(135deg, #FF1493, #DC143C)'
              }
            }}
          >
            Upload
          </Button>
        </DialogActions>
      </Dialog>
    </GlassCard>
  );
};

export default VerificationQueue;
