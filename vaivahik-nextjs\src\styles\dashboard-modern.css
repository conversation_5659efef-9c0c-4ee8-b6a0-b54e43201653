/* Modern Dashboard Styles - Matching Landing Page Design */

/* Import landing page variables */
@import url('./landing-variables.css');

/* Dashboard-specific variables */
:root {
  /* Dashboard Colors - Matching Landing Page */
  --dashboard-primary: #FF5F6D;
  --dashboard-primary-light: #FFC371;
  --dashboard-secondary: #8A2BE2;
  --dashboard-secondary-light: #9370DB;
  --dashboard-accent: #FFD700;
  
  /* Glass morphism effects */
  --glass-bg: rgba(255, 255, 255, 0.95);
  --glass-border: rgba(255, 255, 255, 0.2);
  --glass-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  --glass-backdrop: blur(20px);
  
  /* Dashboard gradients */
  --dashboard-gradient-primary: linear-gradient(135deg, #FF5F6D 0%, #FFC371 100%);
  --dashboard-gradient-secondary: linear-gradient(135deg, #8A2BE2 0%, #9370DB 100%);
  --dashboard-gradient-success: linear-gradient(135deg, #4CAF50 0%, #8BC34A 100%);
  --dashboard-gradient-warning: linear-gradient(135deg, #FF9800 0%, #FFB74D 100%);
  --dashboard-gradient-error: linear-gradient(135deg, #F44336 0%, #EF5350 100%);
  
  /* Shadows */
  --dashboard-shadow-soft: 0 5px 15px rgba(0,0,0,0.05);
  --dashboard-shadow-medium: 0 10px 25px rgba(0,0,0,0.1);
  --dashboard-shadow-hard: 0 15px 35px rgba(0,0,0,0.15);
  --dashboard-shadow-glow: 0 8px 32px rgba(255, 105, 180, 0.3);
  
  /* Transitions */
  --dashboard-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --dashboard-transition-fast: all 0.2s ease;
  --dashboard-transition-slow: all 0.5s ease;
  
  /* Border radius */
  --dashboard-radius-small: 8px;
  --dashboard-radius-medium: 16px;
  --dashboard-radius-large: 24px;
  --dashboard-radius-xl: 32px;
}

/* Dashboard Container */
.dashboard-container {
  min-height: 100vh;
  background: linear-gradient(135deg,
    rgba(255, 182, 193, 0.1) 0%,
    rgba(255, 240, 245, 0.15) 25%,
    rgba(248, 249, 250, 0.2) 50%,
    rgba(255, 228, 225, 0.15) 75%,
    rgba(255, 182, 193, 0.1) 100%
  ),
  radial-gradient(circle at 20% 80%, rgba(255, 105, 180, 0.1) 0%, transparent 50%),
  radial-gradient(circle at 80% 20%, rgba(255, 182, 193, 0.1) 0%, transparent 50%);
  position: relative;
  overflow: hidden;
}

.dashboard-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 25% 25%, rgba(255, 105, 180, 0.05) 0%, transparent 50%),
              radial-gradient(circle at 75% 75%, rgba(255, 182, 193, 0.05) 0%, transparent 50%);
  animation: float 20s ease-in-out infinite;
  z-index: 0;
}

@keyframes float {
  0%, 100% { transform: translate(0, 0) rotate(0deg); }
  33% { transform: translate(30px, -30px) rotate(120deg); }
  66% { transform: translate(-20px, 20px) rotate(240deg); }
}

/* Glass Cards */
.glass-card {
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  -webkit-backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  border-radius: var(--dashboard-radius-large);
  box-shadow: var(--glass-shadow), 0 8px 32px rgba(255, 182, 193, 0.2);
  transition: var(--dashboard-transition);
  position: relative;
  overflow: hidden;
}

.glass-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--dashboard-gradient-primary);
  border-radius: var(--dashboard-radius-large) var(--dashboard-radius-large) 0 0;
}

.glass-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 32px 64px rgba(0, 0, 0, 0.15), 0 16px 48px rgba(255, 182, 193, 0.3);
}

/* Welcome Card */
.welcome-card {
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.98) 0%,
    rgba(255, 240, 245, 0.95) 100%
  );
}

.welcome-card::before {
  background: linear-gradient(90deg, #FF1493, #FF69B4, #FFB6C1);
}

/* Stats Cards */
.stats-card {
  text-align: center;
  padding: 24px;
  transition: var(--dashboard-transition);
}

.stats-card:hover .stats-icon {
  transform: scale(1.2) rotate(10deg);
  filter: drop-shadow(0 8px 16px rgba(255, 105, 180, 0.3));
}

.stats-icon {
  transition: var(--dashboard-transition);
}

/* Match Cards */
.match-card {
  cursor: pointer;
  transition: var(--dashboard-transition);
}

.match-card:hover {
  transform: translateY(-12px) scale(1.02);
}

.match-card:hover .match-avatar {
  transform: scale(1.1);
  filter: brightness(1.1);
}

.match-avatar {
  transition: var(--dashboard-transition);
}

/* Buttons */
.btn-primary {
  background: var(--dashboard-gradient-primary);
  color: white;
  border: none;
  border-radius: var(--dashboard-radius-medium);
  padding: 12px 24px;
  font-weight: 600;
  font-size: 1rem;
  box-shadow: var(--dashboard-shadow-glow);
  transition: var(--dashboard-transition);
  cursor: pointer;
}

.btn-primary:hover {
  background: linear-gradient(135deg, #FF1493 0%, #DC143C 100%);
  transform: translateY(-2px);
  box-shadow: 0 12px 32px rgba(255, 105, 180, 0.4);
}

.btn-secondary {
  background: var(--dashboard-gradient-secondary);
  color: white;
  border: none;
  border-radius: var(--dashboard-radius-medium);
  padding: 12px 24px;
  font-weight: 600;
  font-size: 1rem;
  box-shadow: 0 8px 24px rgba(138, 43, 226, 0.3);
  transition: var(--dashboard-transition);
  cursor: pointer;
}

.btn-secondary:hover {
  background: linear-gradient(135deg, #7B1FA2 0%, #8E24AA 100%);
  transform: translateY(-2px);
  box-shadow: 0 12px 32px rgba(138, 43, 226, 0.4);
}

.btn-outline {
  background: transparent;
  color: var(--dashboard-primary);
  border: 2px solid var(--dashboard-primary);
  border-radius: var(--dashboard-radius-medium);
  padding: 12px 24px;
  font-weight: 600;
  font-size: 1rem;
  transition: var(--dashboard-transition);
  cursor: pointer;
}

.btn-outline:hover {
  background: rgba(255, 95, 109, 0.1);
  border-color: #FF1493;
  transform: translateY(-2px);
}

/* Premium Elements */
.premium-badge {
  background: linear-gradient(135deg, #FFD700, #FFA000);
  color: #000;
  border-radius: var(--dashboard-radius-small);
  padding: 4px 12px;
  font-size: 0.8rem;
  font-weight: 600;
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

.verified-badge {
  background: linear-gradient(135deg, #4CAF50, #8BC34A);
  color: white;
  border-radius: var(--dashboard-radius-small);
  padding: 4px 12px;
  font-size: 0.8rem;
  font-weight: 600;
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

/* Progress Bars */
.progress-bar {
  height: 8px;
  border-radius: 4px;
  background-color: rgba(255, 105, 180, 0.1);
  overflow: hidden;
}

.progress-bar-fill {
  height: 100%;
  background: var(--dashboard-gradient-primary);
  border-radius: 4px;
  transition: width 0.5s ease;
}

.progress-bar-success .progress-bar-fill {
  background: var(--dashboard-gradient-success);
}

.progress-bar-warning .progress-bar-fill {
  background: var(--dashboard-gradient-warning);
}

/* Tabs */
.dashboard-tabs {
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  margin-bottom: 24px;
}

.dashboard-tab {
  padding: 12px 24px;
  border: none;
  background: transparent;
  color: #666;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: var(--dashboard-transition);
  border-bottom: 3px solid transparent;
}

.dashboard-tab:hover {
  color: var(--dashboard-primary);
  background: rgba(255, 95, 109, 0.05);
}

.dashboard-tab.active {
  color: var(--dashboard-primary);
  border-bottom-color: var(--dashboard-primary);
}

/* Floating Action Button */
.fab {
  position: fixed;
  bottom: 24px;
  right: 24px;
  width: 56px;
  height: 56px;
  border-radius: 50%;
  background: var(--dashboard-gradient-primary);
  color: white;
  border: none;
  box-shadow: var(--dashboard-shadow-glow);
  cursor: pointer;
  transition: var(--dashboard-transition);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.fab:hover {
  background: linear-gradient(135deg, #FF1493 0%, #DC143C 100%);
  transform: scale(1.1);
  box-shadow: 0 12px 48px rgba(255, 105, 180, 0.6);
}

/* Responsive Design */
@media (max-width: 768px) {
  .dashboard-container {
    padding: 16px;
  }
  
  .glass-card {
    margin-bottom: 16px;
  }
  
  .stats-card {
    padding: 16px;
  }
  
  .btn-primary,
  .btn-secondary,
  .btn-outline {
    padding: 10px 20px;
    font-size: 0.9rem;
  }
  
  .fab {
    width: 48px;
    height: 48px;
    bottom: 16px;
    right: 16px;
  }
}

@media (max-width: 480px) {
  .dashboard-container {
    padding: 12px;
  }
  
  .glass-card {
    border-radius: var(--dashboard-radius-medium);
  }
  
  .stats-card {
    padding: 12px;
  }
  
  .dashboard-tab {
    padding: 8px 16px;
    font-size: 0.9rem;
  }
}

/* Animation Classes */
.fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

.slide-up {
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from { transform: translateY(100%); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

.scale-in {
  animation: scaleIn 0.3s ease-out;
}

@keyframes scaleIn {
  from { transform: scale(0.8); opacity: 0; }
  to { transform: scale(1); opacity: 1; }
}

/* Loading States */
.loading-skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

/* Utility Classes */
.text-gradient {
  background: var(--dashboard-gradient-primary);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.shadow-glow {
  box-shadow: var(--dashboard-shadow-glow);
}

.border-gradient {
  border: 2px solid transparent;
  background: linear-gradient(white, white) padding-box,
              var(--dashboard-gradient-primary) border-box;
}
