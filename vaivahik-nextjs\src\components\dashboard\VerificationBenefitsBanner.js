/**
 * Verification Benefits Banner Component
 * Shows verification benefits and encourages users to get verified
 */

import { useState } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  Grid,
  Avatar,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Chip,
  Alert,
  IconButton
} from '@mui/material';
import { styled } from '@mui/material/styles';
import {
  Verified as VerifiedIcon,
  Star as StarIcon,
  TrendingUp as TrendingIcon,
  Security as SecurityIcon,
  Visibility as VisibilityIcon,
  Favorite as HeartIcon,
  Shield as ShieldIcon,
  CheckCircle as CheckIcon,
  Close as CloseIcon,
  WorkspacePremium as PremiumIcon,
  LocalFireDepartment as FireIcon
} from '@mui/icons-material';

const GradientCard = styled(Card)(({ theme }) => ({
  background: 'linear-gradient(135deg, rgba(76, 175, 80, 0.1), rgba(139, 195, 74, 0.1))',
  border: '2px solid rgba(76, 175, 80, 0.3)',
  borderRadius: 24,
  boxShadow: `
    0 20px 40px rgba(0, 0, 0, 0.1),
    0 8px 32px rgba(76, 175, 80, 0.2)
  `,
  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
  position: 'relative',
  overflow: 'hidden',
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: 4,
    background: 'linear-gradient(90deg, #4CAF50, #8BC34A, #CDDC39)',
    borderRadius: '24px 24px 0 0'
  },
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: `
      0 32px 64px rgba(0, 0, 0, 0.15),
      0 16px 48px rgba(76, 175, 80, 0.3)
    `
  }
}));

const VerificationBenefitsBanner = ({ isVerified = false, verificationStatus = 'PENDING' }) => {
  const [showBenefitsDialog, setShowBenefitsDialog] = useState(false);

  const verificationBenefits = [
    {
      icon: <TrendingIcon />,
      title: '300% More Profile Views',
      description: 'Verified profiles get significantly more visibility in search results',
      color: '#2196F3'
    },
    {
      icon: <HeartIcon />,
      title: 'Higher Match Quality',
      description: 'Connect with other verified members for serious relationships',
      color: '#E91E63'
    },
    {
      icon: <SecurityIcon />,
      title: 'Trust & Credibility',
      description: 'Build trust with potential matches through verified identity',
      color: '#4CAF50'
    },
    {
      icon: <StarIcon />,
      title: 'Premium Features Access',
      description: 'Unlock exclusive features available only to verified members',
      color: '#FF9800'
    },
    {
      icon: <ShieldIcon />,
      title: 'Enhanced Security',
      description: 'Protect yourself from fake profiles and scammers',
      color: '#9C27B0'
    },
    {
      icon: <FireIcon />,
      title: 'Priority Support',
      description: 'Get faster response times from our customer support team',
      color: '#FF5722'
    }
  ];

  const getStatusMessage = () => {
    switch (verificationStatus) {
      case 'VERIFIED':
        return {
          title: 'Congratulations! You\'re Verified! 🎉',
          message: 'Your profile is now verified and enjoying all premium benefits.',
          severity: 'success',
          action: null
        };
      case 'PENDING':
        return {
          title: 'Verification in Progress ⏳',
          message: 'Your verification is being reviewed. You\'ll be notified once approved.',
          severity: 'info',
          action: 'Track Status'
        };
      case 'REJECTED':
        return {
          title: 'Verification Needs Attention ⚠️',
          message: 'Your verification was rejected. Please resubmit with correct documents.',
          severity: 'warning',
          action: 'Resubmit'
        };
      default:
        return {
          title: 'Get Verified Today! ✨',
          message: 'Join thousands of verified members and boost your profile visibility by 300%!',
          severity: 'info',
          action: 'Start Verification'
        };
    }
  };

  const handleGetVerified = () => {
    if (verificationStatus === 'PENDING') {
      window.location.href = '/verification/status';
    } else if (verificationStatus === 'REJECTED') {
      window.location.href = '/verification/resubmit';
    } else {
      window.location.href = '/verification/start';
    }
  };

  const statusInfo = getStatusMessage();

  if (isVerified && verificationStatus === 'VERIFIED') {
    return (
      <GradientCard>
        <CardContent sx={{ p: 3 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', mb: 2 }}>
            <VerifiedIcon sx={{ color: '#4CAF50', fontSize: 48, mr: 2 }} />
            <Box sx={{ textAlign: 'center' }}>
              <Typography variant="h5" fontWeight="700" color="#4CAF50" gutterBottom>
                Verified Member ✅
              </Typography>
              <Typography variant="body1" color="text.secondary">
                You're enjoying all verification benefits!
              </Typography>
            </Box>
          </Box>
          
          <Grid container spacing={2} sx={{ mt: 2 }}>
            <Grid item xs={6} sm={3}>
              <Box sx={{ textAlign: 'center' }}>
                <Typography variant="h6" fontWeight="700" color="#2196F3">
                  300%
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  More Views
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={6} sm={3}>
              <Box sx={{ textAlign: 'center' }}>
                <Typography variant="h6" fontWeight="700" color="#E91E63">
                  Premium
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  Features
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={6} sm={3}>
              <Box sx={{ textAlign: 'center' }}>
                <Typography variant="h6" fontWeight="700" color="#4CAF50">
                  Trusted
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  Profile
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={6} sm={3}>
              <Box sx={{ textAlign: 'center' }}>
                <Typography variant="h6" fontWeight="700" color="#FF9800">
                  Priority
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  Support
                </Typography>
              </Box>
            </Grid>
          </Grid>
        </CardContent>
      </GradientCard>
    );
  }

  return (
    <>
      <GradientCard>
        <CardContent sx={{ p: 3 }}>
          <Grid container spacing={3} alignItems="center">
            <Grid item xs={12} md={8}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <VerifiedIcon sx={{ color: '#4CAF50', mr: 2, fontSize: 32 }} />
                <Box>
                  <Typography variant="h6" fontWeight="700" gutterBottom>
                    {statusInfo.title}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {statusInfo.message}
                  </Typography>
                </Box>
              </Box>

              <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap', mt: 2 }}>
                {statusInfo.action && (
                  <Button
                    variant="contained"
                    size="large"
                    startIcon={<VerifiedIcon />}
                    onClick={handleGetVerified}
                    sx={{
                      background: 'linear-gradient(135deg, #4CAF50, #8BC34A)',
                      borderRadius: 3,
                      px: 3,
                      py: 1.5,
                      fontSize: '1rem',
                      fontWeight: 600,
                      boxShadow: '0 8px 24px rgba(76, 175, 80, 0.3)',
                      '&:hover': {
                        background: 'linear-gradient(135deg, #388E3C, #689F38)',
                        transform: 'translateY(-2px)',
                        boxShadow: '0 12px 32px rgba(76, 175, 80, 0.4)'
                      }
                    }}
                  >
                    {statusInfo.action}
                  </Button>
                )}
                <Button
                  variant="outlined"
                  size="large"
                  startIcon={<StarIcon />}
                  onClick={() => setShowBenefitsDialog(true)}
                  sx={{
                    borderColor: '#4CAF50',
                    color: '#4CAF50',
                    borderRadius: 3,
                    px: 3,
                    py: 1.5,
                    fontSize: '1rem',
                    fontWeight: 600,
                    '&:hover': {
                      borderColor: '#388E3C',
                      backgroundColor: 'rgba(76, 175, 80, 0.1)',
                      transform: 'translateY(-2px)'
                    }
                  }}
                >
                  View Benefits
                </Button>
              </Box>
            </Grid>

            <Grid item xs={12} md={4} sx={{ textAlign: 'center' }}>
              <Box sx={{ position: 'relative', display: 'inline-block' }}>
                <Avatar
                  sx={{
                    width: 100,
                    height: 100,
                    background: 'linear-gradient(135deg, #4CAF50, #8BC34A)',
                    fontSize: 48
                  }}
                >
                  <VerifiedIcon sx={{ fontSize: 48 }} />
                </Avatar>
                <Chip
                  label={verificationStatus === 'PENDING' ? 'In Review' : 'Get Verified'}
                  size="small"
                  sx={{
                    position: 'absolute',
                    bottom: -8,
                    left: '50%',
                    transform: 'translateX(-50%)',
                    background: verificationStatus === 'PENDING' 
                      ? 'linear-gradient(135deg, #FF9800, #FFB74D)'
                      : 'linear-gradient(135deg, #4CAF50, #8BC34A)',
                    color: 'white',
                    fontWeight: 600
                  }}
                />
              </Box>
            </Grid>
          </Grid>
        </CardContent>
      </GradientCard>

      {/* Benefits Dialog */}
      <Dialog 
        open={showBenefitsDialog} 
        onClose={() => setShowBenefitsDialog(false)} 
        maxWidth="md" 
        fullWidth
        PaperProps={{
          sx: {
            borderRadius: 3,
            background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 249, 250, 0.95))',
            backdropFilter: 'blur(20px)'
          }
        }}
      >
        <DialogTitle sx={{ textAlign: 'center', pb: 1 }}>
          <IconButton
            onClick={() => setShowBenefitsDialog(false)}
            sx={{ position: 'absolute', right: 8, top: 8 }}
          >
            <CloseIcon />
          </IconButton>
          <VerifiedIcon sx={{ color: '#4CAF50', fontSize: 48, mb: 1 }} />
          <Typography variant="h4" fontWeight="700" gutterBottom>
            Verification Benefits
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Discover all the amazing benefits of being a verified member
          </Typography>
        </DialogTitle>

        <DialogContent sx={{ px: 3 }}>
          <Alert severity="success" sx={{ mb: 3, borderRadius: 2 }}>
            <Typography variant="body2" fontWeight="600">
              🚀 Verified members get 300% more profile views and higher quality matches!
            </Typography>
          </Alert>

          <Grid container spacing={3}>
            {verificationBenefits.map((benefit, index) => (
              <Grid item xs={12} sm={6} key={index}>
                <Box sx={{ display: 'flex', alignItems: 'flex-start', p: 2, borderRadius: 2, backgroundColor: 'rgba(255, 255, 255, 0.7)' }}>
                  <Avatar
                    sx={{
                      backgroundColor: `${benefit.color}20`,
                      color: benefit.color,
                      width: 48,
                      height: 48,
                      mr: 2
                    }}
                  >
                    {benefit.icon}
                  </Avatar>
                  <Box>
                    <Typography variant="subtitle1" fontWeight="600" gutterBottom>
                      {benefit.title}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {benefit.description}
                    </Typography>
                  </Box>
                </Box>
              </Grid>
            ))}
          </Grid>

          <Alert severity="info" sx={{ mt: 3, borderRadius: 2 }}>
            <Typography variant="body2">
              💡 <strong>Pro Tip:</strong> Verified profiles are 5x more likely to find their perfect match within 30 days!
            </Typography>
          </Alert>
        </DialogContent>

        <DialogActions sx={{ p: 3, pt: 0 }}>
          <Button onClick={() => setShowBenefitsDialog(false)} size="large">
            Close
          </Button>
          <Button
            variant="contained"
            size="large"
            startIcon={<VerifiedIcon />}
            onClick={() => {
              setShowBenefitsDialog(false);
              handleGetVerified();
            }}
            sx={{
              background: 'linear-gradient(135deg, #4CAF50, #8BC34A)',
              px: 4,
              py: 1.5,
              fontSize: '1.1rem',
              fontWeight: 600,
              '&:hover': {
                background: 'linear-gradient(135deg, #388E3C, #689F38)'
              }
            }}
          >
            Get Verified Now
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default VerificationBenefitsBanner;
