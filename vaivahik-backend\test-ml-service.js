#!/usr/bin/env node

/**
 * ML Service Health and Functionality Test
 * Tests the Python ML matching service integration
 */

const axios = require('axios');
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m'
};

const ML_SERVICE_URL = 'http://localhost:5000';
const BACKEND_URL = 'http://localhost:8080';

function log(type, message) {
  const timestamp = new Date().toISOString();
  const color = type === 'SUCCESS' ? colors.green : 
                type === 'ERROR' ? colors.red : 
                type === 'WARNING' ? colors.yellow : colors.blue;
  console.log(`${color}[${timestamp}] ${type}: ${message}${colors.reset}`);
}

// Test 1: ML Service Health Check
async function testMLServiceHealth() {
  try {
    log('INFO', 'Testing ML service health endpoint...');
    const response = await axios.get(`${ML_SERVICE_URL}/health`, { 
      timeout: 10000,
      validateStatus: () => true 
    });
    
    if (response.status === 200) {
      log('SUCCESS', '✅ ML service health check passed');
      log('INFO', `Response: ${JSON.stringify(response.data)}`);
      return true;
    } else {
      log('ERROR', `❌ ML service health check failed with status: ${response.status}`);
      return false;
    }
  } catch (error) {
    if (error.code === 'ECONNREFUSED') {
      log('ERROR', '❌ ML service is not running or not accessible on port 5000');
    } else if (error.code === 'ECONNRESET') {
      log('WARNING', '⚠️ ML service connection reset - might be restarting');
    } else {
      log('ERROR', `❌ ML service health check failed: ${error.message}`);
    }
    return false;
  }
}

// Test 2: ML Service Matching Endpoint
async function testMLServiceMatching() {
  try {
    log('INFO', 'Testing ML service matching endpoint...');
    
    // Sample user data for testing (correct format for ML API)
    const testUserData = {
      user: {
        id: "test_user_123",
        profile: {
          age: 28,
          height: 5.6,
          education: "Graduate",
          occupation: "Software Engineer",
          income: 800000,
          location: "Mumbai",
          caste: "Maratha"
        }
      },
      preferences: {
        ageMin: 25,
        ageMax: 32,
        heightMin: 5.0,
        heightMax: 6.0
      },
      potentialMatches: [
        {
          id: "match_1",
          profile: {
            age: 26,
            height: 5.4,
            education: "Post Graduate",
            occupation: "Teacher",
            income: 600000,
            location: "Mumbai",
            caste: "Maratha"
          }
        }
      ]
    };

    const response = await axios.post(`${ML_SERVICE_URL}/api/match`, testUserData, {
      timeout: 15000,
      headers: { 'Content-Type': 'application/json' },
      validateStatus: () => true
    });
    
    if (response.status === 200) {
      log('SUCCESS', '✅ ML service matching endpoint working');
      log('INFO', `Matches found: ${response.data.matches ? response.data.matches.length : 'N/A'}`);
      return true;
    } else {
      log('ERROR', `❌ ML service matching failed with status: ${response.status}`);
      log('ERROR', `Response: ${JSON.stringify(response.data)}`);
      return false;
    }
  } catch (error) {
    if (error.code === 'ECONNREFUSED') {
      log('ERROR', '❌ ML service matching endpoint not accessible');
    } else {
      log('ERROR', `❌ ML service matching test failed: ${error.message}`);
    }
    return false;
  }
}

// Test 3: Backend ML Integration
async function testBackendMLIntegration() {
  try {
    log('INFO', 'Testing backend ML service integration...');
    
    // Test if backend can communicate with ML service
    const response = await axios.get(`${BACKEND_URL}/api/health`, {
      timeout: 5000,
      validateStatus: () => true
    });
    
    if (response.status === 200) {
      log('SUCCESS', '✅ Backend is running and can potentially communicate with ML service');
      return true;
    } else {
      log('ERROR', '❌ Backend health check failed');
      return false;
    }
  } catch (error) {
    log('ERROR', `❌ Backend ML integration test failed: ${error.message}`);
    return false;
  }
}

// Test 4: ML Service Performance
async function testMLServicePerformance() {
  try {
    log('INFO', 'Testing ML service response time...');
    
    const startTime = Date.now();
    const response = await axios.get(`${ML_SERVICE_URL}/health`, { timeout: 5000 });
    const endTime = Date.now();
    
    const responseTime = endTime - startTime;
    
    if (responseTime < 1000) {
      log('SUCCESS', `✅ ML service response time excellent: ${responseTime}ms`);
    } else if (responseTime < 3000) {
      log('WARNING', `⚠️ ML service response time acceptable: ${responseTime}ms`);
    } else {
      log('ERROR', `❌ ML service response time poor: ${responseTime}ms`);
    }
    
    return responseTime < 5000; // Consider anything under 5s as working
  } catch (error) {
    log('ERROR', `❌ ML service performance test failed: ${error.message}`);
    return false;
  }
}

// Main test runner
async function runMLTests() {
  console.log(`${colors.blue}
╔══════════════════════════════════════════════════════════════╗
║                    ML SERVICE TEST SUITE                    ║
║              Testing Python ML Service Integration          ║
╚══════════════════════════════════════════════════════════════╝
${colors.reset}`);

  const tests = [
    { name: 'ML Service Health', fn: testMLServiceHealth },
    { name: 'ML Service Performance', fn: testMLServicePerformance },
    { name: 'ML Service Matching', fn: testMLServiceMatching },
    { name: 'Backend ML Integration', fn: testBackendMLIntegration }
  ];

  let passed = 0;
  let total = tests.length;

  for (const test of tests) {
    log('INFO', `\n--- Running ${test.name} Test ---`);
    const result = await test.fn();
    if (result) passed++;
    await new Promise(resolve => setTimeout(resolve, 2000)); // Wait 2 seconds between tests
  }

  // Print summary
  console.log(`${colors.blue}
╔══════════════════════════════════════════════════════════════╗
║                     ML SERVICE SUMMARY                      ║
╚══════════════════════════════════════════════════════════════╝${colors.reset}`);
  
  log('INFO', `Tests Passed: ${passed}/${total}`);
  
  if (passed === total) {
    log('SUCCESS', '🎉 ML SERVICE IS FULLY FUNCTIONAL!');
  } else if (passed >= total * 0.5) {
    log('WARNING', '⚠️ ML SERVICE IS PARTIALLY WORKING - Some issues detected');
  } else {
    log('ERROR', '❌ ML SERVICE HAS SIGNIFICANT ISSUES');
  }

  return passed >= total * 0.5; // Consider 50%+ pass rate as working
}

// Run tests
runMLTests().then(success => {
  process.exit(success ? 0 : 1);
}).catch(error => {
  log('ERROR', `ML test suite failed: ${error.message}`);
  process.exit(1);
});
